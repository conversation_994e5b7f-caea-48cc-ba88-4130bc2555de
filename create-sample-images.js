#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建示例图片的 SVG 内容
function createSampleImage(index) {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
  const color = colors[index % colors.length];
  
  return `<svg width="80" height="80" xmlns="http://www.w3.org/2000/svg">
    <rect width="80" height="80" fill="${color}" opacity="0.8"/>
    <circle cx="40" cy="40" r="20" fill="white" opacity="0.3"/>
    <text x="40" y="45" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">${index + 1}</text>
  </svg>`;
}

// 确保目录存在
const imagesDir = path.join(__dirname, 'src', 'assets', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// 生成示例图片
for (let i = 1; i <= 6; i++) {
  const svgContent = createSampleImage(i);
  const filePath = path.join(imagesDir, `bg${i}.svg`);
  fs.writeFileSync(filePath, svgContent);
  console.log(`创建示例图片: bg${i}.svg`);
}

console.log('示例图片创建完成！');
console.log('你可以将这些 SVG 文件替换为你的实际图片文件。');
console.log('支持的格式: JPG, PNG, GIF, WebP'); 