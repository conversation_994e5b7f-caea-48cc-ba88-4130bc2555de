# 爱情历程时间线功能

## 🎯 功能概述

这是一个浪漫的爱情历程图片墙功能，将你们的重要时刻沿着时间线展示，支持多种交互控制方式。

## ✨ 主要特性

### 1. 爱情历程展示
- **第一次相遇**：记录你们初次见面的美好
- **第一次约会**：回忆第一次约会的甜蜜
- **第一次牵手**：珍藏第一次牵手的温暖
- **第一次拥抱**：铭记第一次拥抱的感动
- **第一次亲吻**：回味第一次亲吻的浪漫
- **现在的我们**：展示现在的幸福时光

### 2. 交互控制方式
- **鼠标滚轮**：上下滚动控制播放速度
- **触摸滑动**：在移动设备上滑动控制方向
- **鼠标移动**：根据鼠标位置调整播放速度
- **键盘控制**：使用方向键或 WASD 控制
- **空格键**：暂停/播放时间线

### 3. 视觉效果
- **S形曲线路径**：图片沿着优美的曲线分布
- **动态高亮**：当前时刻的图片会高亮显示
- **平滑动画**：所有动画都经过优化，流畅自然
- **音乐响应**：播放音乐时动画更加活跃

## 🎮 控制方式详解

### 鼠标滚轮控制
```
向下滚动：向前播放时间线
向上滚动：向后播放时间线
```

### 触摸控制（移动设备）
```
向上滑动：向前播放时间线
向下滑动：向后播放时间线
```

### 键盘控制
```
↑ 或 W：向前播放
↓ 或 S：向后播放
空格键：暂停/播放
```

### 鼠标移动控制
```
鼠标在上半屏：向前播放
鼠标在下半屏：向后播放
```

## 🎨 交互功能

### 点击图片
- 点击任意图片会显示该时刻的标题
- 图片会有缩放动画效果
- 时间线会暂停3秒后自动恢复

### 悬停效果
- 鼠标悬停在图片上会高亮显示
- 显示该时刻的标题
- 透明度会增加

### 标题显示
- 点击或悬停时会显示时间线标题
- 标题显示在屏幕顶部中央
- 2秒后自动消失

## 📱 响应式设计

### 桌面端
- 鼠标滚轮控制
- 键盘快捷键
- 鼠标移动控制

### 移动端
- 触摸滑动控制
- 优化的触摸响应
- 适配不同屏幕尺寸

## 🎵 音乐集成

### 音乐播放
- 点击音乐按钮播放背景音乐
- 音乐播放时动画更加活跃
- 音乐频率影响图片旋转速度

### 音乐响应
- 低音频率：影响旋转速度
- 高音频率：影响动画强度
- 音量大小：影响整体活跃度

## ⚙️ 配置参数

```javascript
const timelineConfig = {
  imageSize: 40,        // 图片大小
  depth: -30,           // 背景深度
  rotationSpeed: 0.0001, // 旋转速度
  opacity: 0.5,         // 透明度
  scale: 0.6,           // 整体缩放
  flyStep: 0.0001,      // 滑动速度
  baseStep: 0.0001      // 基础速度
};
```

## 🎯 自定义选项

### 修改时间线内容
```javascript
const loveTimeline = [
  "你们的第一次相遇",
  "你们的第一次约会", 
  "你们的第一次牵手",
  "你们的第一次拥抱",
  "你们的第一次亲吻",
  "现在的你们"
];
```

### 修改图片路径
```javascript
const imageUrls = [
  "/images/meeting.jpg",    // 相遇照片
  "/images/date.jpg",       // 约会照片
  "/images/hand.jpg",       // 牵手照片
  "/images/hug.jpg",        // 拥抱照片
  "/images/kiss.jpg",       // 亲吻照片
  "/images/now.jpg"         // 现在的照片
];
```

### 调整动画效果
```javascript
// 更快的播放速度
flyStep: 0.0002

// 更慢的播放速度
flyStep: 0.00005

// 更大的图片
imageSize: 60

// 更小的图片
imageSize: 30
```

## 🎨 视觉效果

### 曲线路径
- 使用 CatmullRomCurve3 创建平滑曲线
- 7个控制点形成优美的S形路径
- 图片沿着曲线自然分布

### 动画效果
- 图片缓慢旋转
- 根据播放进度移动位置
- 当前时刻高亮显示
- 音乐响应增强效果

### 交互反馈
- 点击缩放动画
- 悬停高亮效果
- 标题显示动画
- 平滑过渡效果

## 📊 性能优化

### 渲染优化
- 使用 InstancedBufferGeometry
- 优化材质设置
- 减少不必要的计算

### 交互优化
- 节流鼠标事件
- 优化触摸响应
- 减少DOM操作

### 内存管理
- 及时释放资源
- 优化纹理加载
- 控制对象数量

## 🎯 使用建议

### 1. 图片准备
- 使用高质量的照片
- 建议尺寸：400x400 像素
- 支持格式：JPG, PNG, WebP, SVG

### 2. 内容规划
- 选择6-8个重要时刻
- 为每个时刻写简短描述
- 按时间顺序排列

### 3. 交互体验
- 测试不同设备的兼容性
- 调整播放速度适合观看
- 确保音乐文件可用

## 📝 技术实现

### 核心技术
- **Three.js**：3D 图形渲染
- **GSAP**：动画库
- **WebGL**：硬件加速渲染
- **CatmullRomCurve3**：平滑曲线

### 交互技术
- **Raycaster**：鼠标点击检测
- **Touch Events**：触摸控制
- **Keyboard Events**：键盘控制
- **Wheel Events**：滚轮控制

### 动画技术
- **Lerp**：平滑位置插值
- **LookAt**：面向相机
- **Scale Animation**：缩放动画
- **Opacity Transition**：透明度过渡

这个爱情历程时间线功能为你们的重要时刻提供了一个浪漫的展示方式，让每个回忆都变得生动而美好！ 