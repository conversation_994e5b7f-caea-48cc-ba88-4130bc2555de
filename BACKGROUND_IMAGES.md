# 背景图片墙配置指南

## 🖼️ 功能概述

背景图片墙功能在场景中添加了一个由多张图片组成的背景网格，每张图片大小为 80px × 80px，会进行缓慢的旋转动画。

## ⚙️ 配置参数

在 `src/script.js` 的 `addBackgroundImages()` 方法中可以调整以下参数：

```javascript
const imageConfig = {
  size: 80,           // 图片大小（像素）
  spacing: 120,       // 图片间距（像素）
  rows: 6,            // 行数
  cols: 10,           // 列数
  depth: -30,         // 背景深度（Z轴位置）
  rotationSpeed: 0.0005, // 旋转速度
  opacity: 0.2        // 透明度
};
```

## 📁 图片文件管理

### 1. 添加自定义图片

1. 将你的图片文件放入 `src/assets/images/` 目录
2. 支持的格式：JPG, PNG, GIF, WebP, SVG
3. 建议尺寸：80px × 80px（会自动缩放）

### 2. 更新图片路径

在 `src/script.js` 中修改 `imageUrls` 数组：

```javascript
const imageUrls = [
  "./assets/images/your-image1.jpg",
  "./assets/images/your-image2.png",
  "./assets/images/your-image3.svg",
  // 添加更多图片...
];
```

### 3. 图片命名建议

- 使用有意义的文件名
- 避免空格和特殊字符
- 建议使用小写字母和连字符

## 🎨 自定义效果

### 调整网格布局

```javascript
// 更密集的网格
const imageConfig = {
  size: 60,
  spacing: 80,
  rows: 8,
  cols: 12
};

// 更稀疏的网格
const imageConfig = {
  size: 100,
  spacing: 150,
  rows: 4,
  cols: 6
};
```

### 调整动画效果

```javascript
// 更快的旋转
const imageConfig = {
  rotationSpeed: 0.001
};

// 更慢的旋转
const imageConfig = {
  rotationSpeed: 0.0001
};
```

### 调整透明度

```javascript
// 更明显的背景
const imageConfig = {
  opacity: 0.4
};

// 更淡的背景
const imageConfig = {
  opacity: 0.1
};
```

## 🔧 高级自定义

### 1. 添加鼠标交互

在 `loop()` 方法中添加鼠标交互：

```javascript
// 背景图片鼠标交互
if (this.backgroundGroup) {
  this.backgroundGroup.children.forEach((mesh, index) => {
    // 根据鼠标位置调整旋转
    const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
    const mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    
    mesh.rotation.x += mouseY * 0.01;
    mesh.rotation.y += mouseX * 0.01;
  });
}
```

### 2. 添加音乐响应

```javascript
// 根据音乐频率调整旋转速度
if (this.backgroundGroup && this.data) {
  this.backgroundGroup.children.forEach((mesh) => {
    const speedMultiplier = 1 + this.data * 0.5;
    mesh.rotation.x += mesh.userData.rotationSpeed.x * this.time.delta * speedMultiplier;
    mesh.rotation.y += mesh.userData.rotationSpeed.y * this.time.delta * speedMultiplier;
    mesh.rotation.z += mesh.userData.rotationSpeed.z * this.time.delta * speedMultiplier;
  });
}
```

### 3. 添加颜色变化

```javascript
// 根据时间变化颜色
const time = this.time.elapsed;
this.backgroundGroup.children.forEach((mesh, index) => {
  const hue = (time * 0.1 + index * 0.1) % 1;
  const color = new THREE.Color().setHSL(hue, 0.5, 0.5);
  mesh.material.color = color;
});
```

## 🐛 常见问题

### 1. 图片加载失败

- 检查文件路径是否正确
- 确保图片文件存在
- 检查浏览器控制台的错误信息

### 2. 性能问题

- 减少图片数量（rows × cols）
- 降低图片分辨率
- 使用更简单的图片格式

### 3. 图片显示异常

- 检查图片格式是否支持
- 确保图片文件没有损坏
- 尝试使用不同的图片文件

## 📝 示例配置

### 浪漫主题配置

```javascript
const imageConfig = {
  size: 80,
  spacing: 100,
  rows: 8,
  cols: 10,
  depth: -40,
  rotationSpeed: 0.0003,
  opacity: 0.15
};
```

### 动态主题配置

```javascript
const imageConfig = {
  size: 60,
  spacing: 80,
  rows: 10,
  cols: 15,
  depth: -20,
  rotationSpeed: 0.001,
  opacity: 0.3
};
```

## 🎯 最佳实践

1. **图片选择**：选择主题相关的图片
2. **性能优化**：控制图片数量和大小
3. **视觉平衡**：调整透明度和旋转速度
4. **响应式设计**：考虑不同屏幕尺寸
5. **加载优化**：使用适当的图片格式和压缩

## 📚 相关资源

- [Three.js 纹理加载](https://threejs.org/docs/#api/en/loaders/TextureLoader)
- [WebGL 纹理最佳实践](https://webglfundamentals.org/webgl/lessons/webgl-image-processing.html)
- [图片优化指南](https://web.dev/fast/#optimize-your-images) 