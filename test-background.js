#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎨 背景图片墙配置测试');
console.log('========================');

// 检查图片文件是否存在
const imagesDir = path.join(__dirname, 'src', 'assets', 'images');
const imageFiles = ['bg1.svg', 'bg2.svg', 'bg3.svg', 'bg4.svg', 'bg5.svg', 'bg6.svg'];

console.log('\n📁 检查图片文件:');
imageFiles.forEach(file => {
  const filePath = path.join(imagesDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
  }
});

console.log('\n⚙️ 当前配置参数:');
console.log('- 图片大小: 80px × 80px');
console.log('- 网格布局: 6行 × 10列');
console.log('- 图片间距: 100px');
console.log('- 背景深度: -20');
console.log('- 透明度: 0.4');
console.log('- 旋转速度: 0.0003');

console.log('\n🎯 优化建议:');
console.log('1. 如果图片仍然看不清，可以进一步增加透明度到 0.5-0.6');
console.log('2. 如果旋转太快，可以降低 rotationSpeed 到 0.0001');
console.log('3. 如果图片太密集，可以增加 spacing 到 120-150');
console.log('4. 如果图片太小，可以增加 size 到 100-120');

console.log('\n🔧 快速调整方法:');
console.log('编辑 src/script.js 中的 imageConfig 对象:');
console.log('```javascript');
console.log('const imageConfig = {');
console.log('  size: 80,           // 调整图片大小');
console.log('  spacing: 100,       // 调整间距');
console.log('  opacity: 0.4,       // 调整透明度');
console.log('  rotationSpeed: 0.0003, // 调整旋转速度');
console.log('  depth: -20          // 调整背景深度');
console.log('};');
console.log('```');

console.log('\n✨ 测试完成！访问 http://localhost:3000 查看效果'); 