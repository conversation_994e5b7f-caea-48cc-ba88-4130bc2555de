# 爱情历程时间线实现

## 🎯 功能概述

参考 `test.html` 的实现方式，成功实现了爱情历程时间线功能。图片从远处缓缓驶来，点击查看有详细的文字描述。

## ✨ 核心特性

### 1. 沿曲线移动的相机
- **SplineCamera**: 使用 `THREE.PerspectiveCamera` 沿 `CatmullRomCurve3` 曲线移动
- **平滑移动**: 相机沿着预定义的曲线路径平滑移动
- **动态视角**: 相机始终朝向曲线的前方，提供沉浸式体验

### 2. 时间线展示
- **6个重要时刻**: 从第一次相遇到现在的我们
- **S形曲线路径**: 图片沿着优美的S形曲线分布
- **深度层次**: 图片位于不同深度，营造3D空间感

### 3. 交互控制
- **鼠标滚轮**: 控制播放速度和方向
- **触摸滑动**: 移动设备上的滑动控制
- **鼠标移动**: 根据鼠标位置调整播放速度
- **键盘控制**: 方向键、WASD、空格键控制

### 4. 弹窗详情
- **点击查看**: 点击任意图片显示详细信息
- **文字描述**: 每个时刻都有详细的文字描述
- **图片展示**: 弹窗中显示大图
- **关闭功能**: 点击关闭按钮或按ESC键关闭

## 🎮 控制方式

### 鼠标滚轮控制
```
向下滚动：向前播放时间线
向上滚动：向后播放时间线
```

### 触摸控制（移动设备）
```
向上滑动：向前播放时间线
向下滑动：向后播放时间线
```

### 键盘控制
```
↑ 或 W：向前播放
↓ 或 S：向后播放
空格键：暂停/播放
ESC：关闭弹窗
```

### 鼠标移动控制
```
鼠标在上半屏：向前播放
鼠标在下半屏：向后播放
```

## 🎨 视觉效果

### 相机移动
```javascript
// 相机沿曲线移动
const t = this.playPercentage;
const position = new THREE.Vector3();
this.timelineCurve.getPointAt(t, position);
position.multiplyScalar(this.timelineConfig.scale);
this.splineCamera.position.copy(position);

// 计算相机朝向
this.timelineCurve.getPointAt((t + 30 / this.timelineCurve.getLength()) % 1, lookAt);
lookAt.multiplyScalar(this.timelineConfig.scale);
this.splineCamera.lookAt(lookAt);
```

### 图片分布
```javascript
// 沿着曲线放置图片
const curve = new THREE.CatmullRomCurve3([
  new THREE.Vector3(-300, 0, 0),
  new THREE.Vector3(-300, 0, 300),
  new THREE.Vector3(-300, -300, 300),
  new THREE.Vector3(-300, -300, 600),
  new THREE.Vector3(-300, 0, 600),
  new THREE.Vector3(0, 0, 600),
  new THREE.Vector3(300, 0, 600),
  new THREE.Vector3(300, 0, 300),
  new THREE.Vector3(300, 0, 0)
]);
```

### 透明度变化
```javascript
// 根据距离相机的远近调整透明度
const distanceToCamera = mesh.position.distanceTo(this.splineCamera.position);
const maxDistance = 1000;
const minDistance = 100;

if (distanceToCamera < maxDistance) {
  const opacity = Math.max(0.1, 1 - (distanceToCamera - minDistance) / (maxDistance - minDistance));
  mesh.material.opacity = this.timelineConfig.opacity * opacity;
} else {
  mesh.material.opacity = 0.1;
}
```

## 📱 弹窗功能

### HTML结构
```html
<div class="memorabilia-layer" id="memorabilia-layer">
  <div class="arrow">
    <a href="javascript:;" class="prev" onclick="closePopup()">×</a>
    <a href="javascript:;" class="next" onclick="closePopup()">×</a>
  </div>
  <div class="container">
    <div class="content">
      <div class="year" id="popup-title"></div>
      <div class="description" id="popup-description"></div>
    </div>
    <div class="imgs">
      <img id="popup-image" src="" alt="">
    </div>
  </div>
</div>
```

### JavaScript控制
```javascript
function openpop(showyear) {
  const content = {
    '第一次相遇': {
      title: '第一次相遇',
      description: '那是一个美好的下午，我们在咖啡厅相遇了...',
      image: '/images/bg1.svg'
    },
    // ... 其他时刻
  };
  
  if (content[showyear]) {
    title.textContent = content[showyear].title;
    description.textContent = content[showyear].description;
    image.src = content[showyear].image;
    layer.classList.add('show');
  }
}
```

### CSS样式
```css
.memorabilia-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
}

.memorabilia-layer.show {
  display: flex;
}
```

## ⚙️ 配置参数

### 时间线配置
```javascript
const timelineConfig = {
  imageSize: 60,        // 图片大小
  depth: -200,          // 背景深度（更远）
  rotationSpeed: 0.0001, // 旋转速度
  opacity: 0.8,         // 透明度
  scale: 1.0,           // 整体缩放
  flyStep: 0.0001,      // 滑动速度
  baseStep: 0.0001      // 基础速度
};
```

### 爱情历程内容
```javascript
const loveTimeline = [
  "第一次相遇",
  "第一次约会", 
  "第一次牵手",
  "第一次拥抱",
  "第一次亲吻",
  "现在的我们"
];
```

## 🎯 技术实现

### 核心技术
- **Three.js**: 3D 图形渲染
- **CatmullRomCurve3**: 平滑曲线路径
- **SplineCamera**: 沿曲线移动的相机
- **Raycaster**: 鼠标点击检测
- **GSAP**: 动画库

### 关键算法
1. **相机移动**: 根据播放进度计算相机位置
2. **图片分布**: 沿着曲线均匀分布图片
3. **透明度计算**: 根据距离相机远近调整透明度
4. **交互检测**: 使用射线检测实现点击交互

### 性能优化
- **距离计算**: 优化图片透明度计算
- **渲染优化**: 使用 splineCamera 进行渲染
- **内存管理**: 及时释放不需要的资源

## 📊 用户体验

### 沉浸式体验
- 相机沿曲线移动，营造穿越时空的感觉
- 图片从远处缓缓驶来，增强期待感
- 点击查看详情，深入了解每个时刻

### 交互反馈
- 鼠标悬停高亮显示
- 点击缩放动画效果
- 弹窗平滑显示和隐藏

### 响应式设计
- 适配桌面和移动设备
- 触摸控制优化
- 弹窗在不同屏幕尺寸下的适配

这个实现完全参考了 `test.html` 的设计理念，成功创建了一个浪漫的爱情历程时间线，让用户可以通过多种方式控制播放，点击查看详细信息，体验沉浸式的爱情故事展示。 