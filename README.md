# With Love - 小宝贝和小轩轩

一个浪漫的 Three.js 动画项目，使用 WebGL Shaders 和 GSAP 创建。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
启动开发服务器，访问 `http://localhost:3000`

### 构建生产版本
```bash
npm run build
```
构建后的文件将输出到 `dist/` 目录

### 预览生产版本
```bash
npm run preview
```
预览构建后的项目，访问 `http://localhost:4173`

## 📁 项目结构

```
with-love/
├── src/                    # 开发源代码
│   ├── index.html         # 主页面
│   ├── script.js          # 主要逻辑
│   └── style.css          # 样式文件
├── dist/                   # 构建输出目录
├── package.json           # 项目配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 🛠️ 开发工作流

### 1. 修改源代码
- 在 `src/` 目录下修改文件
- 支持热重载，修改后自动刷新

### 2. 构建项目
```bash
npm run build
```

### 3. 部署
将 `dist/` 目录的内容部署到服务器

## 🎨 技术栈

- **Three.js** - 3D 图形库
- **GSAP** - 动画库
- **WebGL Shaders** - 自定义着色器
- **Vite** - 构建工具

## 🎵 功能特性

- 3D 爱心动画
- 音乐播放功能
- 粒子效果
- 响应式设计
- 鼠标交互

## 📝 修改指南

### 修改标题
编辑 `src/index.html` 中的 `<h1>` 标签

### 修改颜色
编辑 `src/script.js` 中的 `colorChoices` 数组

### 修改动画参数
编辑 `src/script.js` 中的 `parameters` 对象

### 修改音乐
替换 `dist/live.ogg` 文件

## 🔧 自定义配置

### 修改 Vite 配置
编辑 `vite.config.js` 文件

### 修改构建输出
在 `vite.config.js` 中调整 `build.outDir` 配置

## 📄 许可证

MIT License - 详见 [LICENSE.txt](LICENSE.txt)

## 🙏 致谢

- 原始项目：[https://codepen.io/leiwenxuan/pen/VYvpJVX](https://codepen.io/leiwenxuan/pen/VYvpJVX)
- 基于：[https://codepen.io/pehaa/pen/wvPgboY](https://codepen.io/pehaa/pen/wvPgboY) 