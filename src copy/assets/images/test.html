<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js S-Curve Spheres with Scroll</title>
    <style>
        body {
            margin: 0;
        }

        canvas {
            display: block;
        }
    </style>
</head>

<body class="show" style="cursor: default;">
    <div class="memorabilia" id="memorabilia">
        <div class="tip" style="display: none;"></div>
        <div class="bg1" style="opacity: 0.1923;"></div>
        <!-- <canvas width="526" height="730" style="display: block; width: 526px; height: 730px;"></canvas> -->
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            // 确保 jQuery 已经加载
            if ($(window).width() <= 1279) {
                // 假设 .list 是 <a> 标签的兄弟元素，并且我们想要修改这个 <a> 标签的 href
                $(".g-nav li .list").prev('a').each(function () {
                    $(this).attr('href', 'javascript:;');
                });
            }
        });
    </script>
    <script type="x-shader/x-vertex" id="vertexshader">
            uniform float amplitude;
            attribute float size;
            attribute vec3 customColor;
            varying vec3 vColor;
            void main() {
                vColor = customColor;
                vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
                gl_PointSize = size * ( 300.0 / -mvPosition.z );
                gl_Position = projectionMatrix * mvPosition;
            }
        </script>
    <script type="x-shader/x-fragment" id="fragmentshader">
            uniform vec3 color;
            uniform sampler2D texture;
            varying vec3 vColor;
            void main() {
                gl_FragColor = vec4( color * vColor, 1.0 );
                gl_FragColor = gl_FragColor * texture2D( texture, gl_PointCoord );
            }
        </script>
    <script>
        //实施的时候注意，使用这个方法
        function openpop(showyear) {
            if ($("[data-year='" + showyear + "']").length == 0) return;
            $(".memorabilia-layer").addClass("show");
            $(".memorabilia-layer .item").removeClass("cur");
            $("[data-year='" + showyear + "']").show();
            $("[data-year='" + showyear + "']").addClass("cur");
        }
    </script>
    <script>
        console.log("jinx zhong ")
        var scene = new THREE.Scene();
        var aspect = window.innerWidth / window.innerHeight;
        var renderer = new THREE.WebGL1Renderer({ antialias: true, alpha: true, preserveDrawingBuffer: true }); //
        renderer.setClearColor(0x000000, 0); // the default
        renderer.setClearAlpha(0.0);

        //Generate Point Texure from a canvas element
        var baseMaterial = generatePointTexture();
        renderer.setSize(window.innerWidth, window.innerHeight);
        scene.background = new THREE.Color(0xEAEAEA);
        document.body.appendChild(renderer.domElement);
        document.getElementById("memorabilia").appendChild(renderer.domElement);
        var myline = new THREE.CatmullRomCurve3([
            // new THREE.Vector3(322.707672,69.441139,41.219769),
            // new THREE.Vector3(329.788177,69.254837,45.598007),
            // new THREE.Vector3(336.836700,68.980263,50.188435),
            // new THREE.Vector3(343.855682,68.601288,54.919643),
            // new THREE.Vector3(350.849762,68.099274,59.710560),
            // new THREE.Vector3(357.825470,67.454544,64.474297),
            // new THREE.Vector3(364.788635,66.649445,69.125763),
            // new THREE.Vector3(371.744843,65.669022,73.583496),
            // new THREE.Vector3(378.698608,64.501060,77.769569),
            // new THREE.Vector3(385.654053,63.136005,81.609634),
            // new THREE.Vector3(392.614441,61.567059,85.032936),
            // new THREE.Vector3(399.582520,59.790092,87.972298),
            // new THREE.Vector3(406.560150,57.803699,90.364075),
            // new THREE.Vector3(413.548737,55.609203,92.148300),
            // new THREE.Vector3(420.548828,53.210598,93.268448),
            // new THREE.Vector3(427.560425,50.614624,93.671684),
            // new THREE.Vector3(434.582733,47.830700,93.308701),
            // new THREE.Vector3(441.614349,44.870964,92.133781),
            // new THREE.Vector3(448.653290,41.750275,90.104774),
            // new THREE.Vector3(455.696594,38.486176,87.183105),
            // new THREE.Vector3(462.740936,35.098938,83.333786),
            // new THREE.Vector3(469.782196,31.611536,78.525421),
            // new THREE.Vector3(476.816711,28.050081,72.730972),
            // new THREE.Vector3(483.864227,24.451849,65.942993),
            // new THREE.Vector3(491.000153,20.876785,58.195442),
            // new THREE.Vector3(498.360565,17.409203,49.566792),

            new THREE.Vector3(300, 0, 0),
            new THREE.Vector3(300, 0, 300),
            new THREE.Vector3(300, -300, 300),
            new THREE.Vector3(300, -300, 600),
            new THREE.Vector3(300, 0, 600)
        ]);

        var params = {
            spline: 'MyLine',
            scale: 2,
            extrusionSegments: 100,
            radiusSegments: 5,
            closed: false,
            animationView: true,
            lookAhead: false,
            cameraHelper: false,
            showPath: false,
            moveForward: true,
            moveBackward: false,
        };

        var percentage = 0;
        var yeararray = ["", 1911, 1912, 1919, 1925, 1926, 1928, 1931, 1933, 1935, 1937, 1946, 1948, 1949, 1950, 1952, 1953, 1954, 1955, 1958, 1960, 1970, 1977, 1978, 1979, 1984, 1985, 1990, 1996, 1998, 1999, 2001, 2008, 2009, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, ""
        ]
        var ptNum = yeararray.length;
        var baseStep = 0.0001;
        var flyStep = baseStep;
        var cylinder = [];

        var selectedObject = null;
        group = new THREE.Group();
        scene.add(group);

        var shadermaterial = new THREE.ShaderMaterial({
            uniforms: {
                amplitude: { value: 1.0 },
                color: { value: new THREE.Color(0xFFFFFF) },
                texture: { value: baseMaterial }
            },
            vertexShader: document.getElementById('vertexshader').textContent,
            fragmentShader: document.getElementById('fragmentshader').textContent,
            depthTest: false,
            transparent: true
        });

        var direction = new THREE.Vector3();
        var binormal = new THREE.Vector3();
        var normal = new THREE.Vector3();
        var position = new THREE.Vector3();
        var lookAt = new THREE.Vector3();

        var mesh;
        var material = new THREE.MeshLambertMaterial({ color: 0xff00ff });
        var wireframeMaterial = new THREE.MeshBasicMaterial({ color: 0x000000, opacity: 0.3, wireframe: true, transparent: true });

        // tube
        var parent = new THREE.Object3D();
        scene.add(parent);

        var splineCamera = new THREE.PerspectiveCamera(60, aspect, 0.01, 1000);
        parent.add(splineCamera);

        addTube();
        genPoints(scene);

        function addTube() {
            if (mesh !== undefined) {
                parent.remove(mesh);
                mesh.geometry.dispose();
            }
            var extrudePath = myline;
            tubeGeometry = new THREE.TubeBufferGeometry(extrudePath, params.extrusionSegments, 2, params.radiusSegments, params.closed);
            addGeometry(tubeGeometry);
            setScale();
            extrudePath.visible = params.showPath;
            tubeGeometry.visible = params.showPath;
        }

        function setScale() {
            mesh.scale.set(params.scale, params.scale, params.scale);
        }

        function addGeometry(geometry) {
            // 3D shape
            mesh = new THREE.Mesh(geometry, material);
            var wireframe = new THREE.Mesh(geometry, wireframeMaterial);
            mesh.add(wireframe);
            parent.add(mesh);
            mesh.visible = params.showPath;
            geometry.visible = params.showPath;
            wireframe.visible = params.showPath;
        }

        function genPoints(scene, size = 4, transparent = true, opacity = 0.6, vertexColors = true, sizeAttenuation = false, colorValue = 0xbb9c9c, vertexColorValue = 0xff0000) {

            var ptvertices = [];
            var ptcolors = [];
            var geom = new THREE.BufferGeometry();
            var material = new THREE.PointsMaterial({
                size: size,
                transparent: transparent,
                opacity: opacity,
                vertexColors: vertexColors,
                sizeAttenuation: sizeAttenuation,
                color: new THREE.Color(colorValue)
            });

            var range = 3000;
            var color = new THREE.Color(vertexColorValue);
            var sizes = [];

            for (var i = 0; i < 10000; i++) {
                cylinder[i] = new THREE.Mesh(new THREE.CylinderGeometry(0, 3, 7, 3, 1, true), new THREE.MeshBasicMaterial({ color: Math.random() * 0xef3523 }));
                //scene.add( cylinder[i]);
                cylinder[i].position.set(Math.random() * range - 0 / 2, Math.random() * range - range / 2, Math.random() * range - range / 2);
                cylinder[i].rotation.set(Math.random() * range - 0 / 2, Math.random() * range - range / 2, Math.random() * range - range / 2);
                scene.add(cylinder[i]);
            }
        }

        const raycaster = new THREE.Raycaster();
        const pointer = new THREE.Vector2();

        document.getElementById("memorabilia").addEventListener('touchstart', onMouseDown, { passive: true });
        document.getElementById("memorabilia").addEventListener('mousedown', onMouseDown, { passive: true });
        document.getElementById("memorabilia").addEventListener('pointermove', onCursorMove, { passive: true });
        document.getElementById("memorabilia").addEventListener('mousewheel', onMouseScroll, { passive: true });
        document.getElementById("memorabilia").addEventListener('touchmove', onTouchMove, { passive: true });

        function onTouchMove(event) {

            var touches = event.changedTouches;
            onPointerMove(touches[0]);

        }



        function onCursorMove(event) {
            console.log("onCursorMove zhong ")

            var mouse = new THREE.Vector2();
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = - (event.clientY / window.innerHeight) * 2 + 1;

            raycaster.setFromCamera(mouse, splineCamera);
            var intersects = raycaster.intersectObjects(group);
            if (intersects.length > 0) {
                const elements = document.querySelectorAll('html, body');
                elements.forEach(element => {
                    element.style.cursor = 'pointer';
                });
                // $('html,body').css('cursor', 'pointer');
            } else {
                const elements = document.querySelectorAll('html, body');
                elements.forEach(element => {
                    element.style.cursor = 'default';
                });
                // $('html,body').css('cursor', 'default');
            }

        }
        function onPointerMove(event) {
            var a = (event.clientY - window.innerHeight / 2) / 200000;
            if (event.clientY < window.innerHeight / 2) {
                flyStep = baseStep;
                flyStep -= a;
            }
            else {
                flyStep = -baseStep;
                flyStep -= a;
            }
        }

        var step = 0;
        function onMouseScroll(event) {
            var delta = 0;
            if (!event) event = window.event;
            if (event.wheelDelta) {//IE、chrome浏览器使用的是wheelDelta，并且值为“正负120”
                delta = event.wheelDelta / 120;
                if (window.opera) delta = -delta;//因为IE、chrome等向下滚动是负值，FF是正值，为了处理一致性，在此取反处理
            } else if (event.detail) {//FF浏览器使用的是detail,其值为“正负3”
                delta = -event.detail / 3;
            }
            if (delta > 0) {//向下滚动
                if (step > 0) step = 0;
                flyStep = baseStep;
                flyStep -= (step / 100000);
                step--;
            } else {//向上滚动
                if (step < 0) step = 0;
                flyStep = -baseStep;
                flyStep -= (step / 100000);
                step++;
            }
        }


        function onMouseDown(event) {

            if (selectedObject) {
                selectedObject.material.color.set('white');
                selectedObject = null;
            }

            pointer.x = (event.clientX / window.innerWidth) * 2 - 1;
            pointer.y = - (event.clientY / window.innerHeight) * 2 + 1;

            raycaster.setFromCamera(pointer, splineCamera);
            const intersects = raycaster.intersectObject(group, true);
            if (intersects.length > 0) {
                const res = intersects.filter(function (res) {
                    return res && res.object;
                })[0];
                if (res && res.object) {
                    selectedObject = res.object;
                    selectedObject.material.color.set('yellow');

                    openpop(selectedObject.userData.id);
                    flyStep = 0;
                }
            }
            else {
                if (event.clientY < window.innerHeight / 2) flyStep = baseStep;
                else flyStep = -baseStep;
            }
        }

        function makeLabelCanvas(baseWidth, size, name, bkcolor, color) {
            const borderSize = 2;
            const ctx = document.createElement('canvas').getContext('2d');
            const font = `bold ${size}px myFont,IMPACT`;
            ctx.font = font;
            // measure how long the name will be
            const textWidth = ctx.measureText(name).width;

            const doubleBorderSize = borderSize * 2;
            const width = baseWidth + doubleBorderSize;
            const height = size + doubleBorderSize;
            ctx.canvas.width = width;
            ctx.canvas.height = height;

            // need to set font again after resizing canvas
            ctx.font = font;
            ctx.textBaseline = 'middle';
            ctx.textAlign = 'center';

            ctx.fillStyle = bkcolor;
            ctx.fillRect(0, 0, width, height);

            // scale to fit but don't stretch
            const scaleFactor = Math.min(1, baseWidth / textWidth);
            ctx.translate(width / 2, height / 2);
            ctx.scale(scaleFactor, 1);
            ctx.fillStyle = color;
            ctx.fillText(name, 0, 0);

            return ctx.canvas;
        }

        function makeLabel(x, y, z, labelWidth, size, name, bkcolor, color) {
            const canvas = makeLabelCanvas(labelWidth, size, name, bkcolor, color);
            const texture = new THREE.CanvasTexture(canvas);

            texture.minFilter = THREE.LinearFilter;
            texture.wrapS = THREE.ClampToEdgeWrapping;
            texture.wrapT = THREE.ClampToEdgeWrapping;

            const labelMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
            });

            const labelBaseScale = 0.01;
            const label = new THREE.Sprite(labelMaterial);
            label.position.set(x, y, z);
            label.scale.set(canvas.width * labelBaseScale, canvas.height * labelBaseScale, 1);
            label.userData = { id: name };
            group.add(label);
        }

        // var  loader = new THREE.TextureLoader();
        // const bgColor = loader.load( 'bkcolor.jpg' );

        // loader = new THREE.TextureLoader();
        // const bgGray = loader.load( 'bkgray.jpg' );

        // loader = new THREE.TextureLoader();
        // const bgGray1 = loader.load( 'bkgray1.jpg' );

        // loader = new THREE.TextureLoader();
        // const bgGray2 = loader.load( 'bkgray2.jpg' );

        // loader = new THREE.TextureLoader();
        // const bgGray3 = loader.load( 'bkgray3.jpg' );

        // loader = new THREE.TextureLoader();
        // const bgGray4 = loader.load( 'bkgray4.jpg' );

        var playpercentage = 0;
        flyStep = baseStep;

        function myrender() {

            if (params.animationView === true) {
                if (flyStep > 0 && playpercentage >= 1 - 2 / ptNum) {
                    playpercentage = 1 - 2 / ptNum;
                }
                else if (flyStep < 0 && playpercentage <= 1 / ptNum) {
                    playpercentage = 1 / ptNum;
                }
                playpercentage += flyStep;

                var t = playpercentage;

                $("#memorabilia .bg1").css("opacity", playpercentage);
                console.log("memorabilia opacity ", playpercentage)
                tubeGeometry.parameters.path.getPointAt(t, position);
                position.multiplyScalar(params.scale);

                // if( playpercentage > 0.2 )
                //   scene.background = bgColor;
                // else if( playpercentage > 0.198 )
                //   scene.background = bgGray4;
                // else if( playpercentage > 0.196 )
                //   scene.background = bgGray3;
                // else if( playpercentage > 0.194 )
                //   scene.background = bgGray2;
                // else if( playpercentage > 0.192 )
                //   scene.background = bgGray1;
                // else
                //   scene.background = bgGray;

                // scene.background = bgColor;

                // interpolation
                var segments = tubeGeometry.tangents.length;
                var pickt = t * segments;
                var pick = Math.floor(pickt);
                var pickNext = (pick + 1) % segments;

                binormal.subVectors(tubeGeometry.binormals[pickNext], tubeGeometry.binormals[pick]);
                binormal.multiplyScalar(pickt - pick).add(tubeGeometry.binormals[pick]);

                tubeGeometry.parameters.path.getTangentAt(t, direction);
                var offset = 10;

                normal.copy(binormal).cross(direction);

                position.add(binormal.clone().multiplyScalar(offset + 5));

                position.add(normal.clone().multiplyScalar(offset));

                splineCamera.position.copy(position);

                tubeGeometry.parameters.path.getPointAt((t + 30 / tubeGeometry.parameters.path.getLength()) % 1, lookAt);
                lookAt.multiplyScalar(params.scale);

                if (!params.lookAhead) lookAt.copy(position).add(direction);
                splineCamera.matrix.lookAt(splineCamera.position, lookAt, normal);
                splineCamera.quaternion.setFromRotationMatrix(splineCamera.matrix);
            }


            for (var i = 0; i < cylinder.length; i++) {
                cylinder[i].rotation.x += 0.02;
                cylinder[i].rotation.y += 0.02;
                cylinder[i].rotation.z += 0.02;
            }




            renderer.render(scene, params.animationView === true ? splineCamera : camera);
        }

        //Handle resize
        window.addEventListener("resize", onWindowResize, false);
        var SceneBuilder = function () {
            this.xIterations = 10;
            this.zIterations = 10;
            this.startColor = "#00FF00";
            this.endColor = "#FF0000";
            this.minSize = 1;
            this.maxSize = 5;
            var _this = this;
            this.rebuild = function () {
                var toDelete = scene.getObjectByName("points");
                if (typeof toDelete !== "undefined") {
                    scene.remove(toDelete);
                }

                var vertices = [];
                var colors = [];
                var sizes = [];
                var year = 1908;
                var bkcolor1 = new THREE.Color(0xb700c7);
                var bkcolor2 = new THREE.Color(0xf00000);
                var bkcolor3 = new THREE.Color(0x23d600);
                var bkcolor4 = new THREE.Color(0xffa200);
                for (var i = 0; i < ptNum; i++) {
                    percentage += 1 / ptNum;
                    var p1 = myline.getPointAt(percentage % 1);
                    vertices.push(p1.x * params.scale, p1.y * params.scale, p1.z * params.scale);
                    sizes.push(3);
                    if (percentage > 0.75) {
                        colors.push(bkcolor1.r, bkcolor1.g, bkcolor1.b);
                    }
                    else if (percentage > 0.5) {
                        colors.push(bkcolor2.r, bkcolor2.g, bkcolor2.b);
                    }
                    else if (percentage > 0.25) {
                        colors.push(bkcolor3.r, bkcolor3.g, bkcolor3.b);
                    }
                    else if (percentage > 0) {
                        colors.push(bkcolor4.r, bkcolor4.g, bkcolor4.b);
                    }

                    //if( i % 2 )
                    if (yeararray[i].toString().length > 0) makeLabel(p1.x * params.scale, p1.y * params.scale - 8, p1.z * params.scale, 1200, 600, yeararray[i].toString(), 'transparent', 'white');
                    //year++;
                }

                var geometry = new THREE.BufferGeometry();
                geometry.setAttribute("position", new THREE.BufferAttribute(new Float32Array(vertices), 3));
                geometry.setAttribute("customColor", new THREE.BufferAttribute(new Float32Array(colors), 3));
                geometry.setAttribute("size", new THREE.BufferAttribute(new Float32Array(sizes), 1));
                geometry.computeBoundingSphere();

                //#region Add to Scene
                var points = new THREE.Points(geometry, shadermaterial);
                points.name = "points";
                scene.add(points);
            };
        };
        //show in beginning
        var sceneBuilder = new SceneBuilder();
        sceneBuilder.rebuild();

        function onWindowResize() {
            aspect = window.innerWidth / window.innerHeight;
            splineCamera.aspect = aspect;
            splineCamera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        function generatePointTexture() {
            var canvas = document.createElement("canvas");
            canvas.width = canvas.height = 64;
            var ctx = canvas.getContext("2d");
            var tex = new THREE.Texture(canvas);
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, canvas.height / 2 - 3, 0, 2 * Math.PI);
            ctx.fillStyle = "#FFFFFF";
            ctx.fill();
            tex.needsUpdate = true;
            if (tex.minFilter !== THREE.NearestFilter && tex.minFilter !== THREE.LinearFilter) {
                tex.minFilter = THREE.NearestFilter;
            }
            return tex;
        }

        function animate() {

            myrender();
            requestAnimationFrame(animate);
        }

        window.addEventListener("load", function () {
            animate();
            setTimeout(function () { $(".memorabilia .tip").fadeOut(); }, 3000);
        }, false);
    </script>
    <style>
        /* @font-face {
			font-family: myFont;
			src: url(image/font/IMPACT.ttf) format("truetype"), url(image/font/IMPACT.otf) format("opentype")
		} */

        .memorabilia canvas {
            position: relative;
            z-index: 1;
        }

        .memorabilia .bg1 {
            position: absolute;
            z-index: 0;
            height: 100%;
            width: 100%;
            background: url(image/memorabilia.jpg) center no-repeat;
            background-size: cover;
        }

        .memorabilia {
            position: relative;
            background: url(image/memorabilia.jpg) center no-repeat;
            background-size: cover;
        }

        .memorabilia-layer .content {
            max-height: 2rem;
            overflow-y: auto;
        }

        .memorabilia-layer .content::-webkit-scrollbar {
            width: 8px;
            background-color: #f0f0f0;
        }

        .memorabilia-layer .content::-webkit-scrollbar-thumb {
            background: #bfbfbf;
        }

        .memorabilia-layer .container .year {
            z-index: 0;
        }

        .memorabilia .tip {
            background: url(image/wheel.png) center no-repeat;
            background-size: cover;
            height: 165px;
            width: 113px;
            position: absolute;
            left: 50%;
            top: 50%;
            margin-top: -82px;
            margin-left: -57px;
            z-index: 2;
        }

        .memorabilia-layer .arrow a {
            display: block;
            height: 80px;
            width: 20px;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            z-index: 1;
            left: auto;
            right: auto;
            top: 50%;
            margin-top: -30px;
            cursor: pointer;
        }

        .memorabilia-layer .arrow a:hover {
            opacity: 0.4;
        }

        .memorabilia-layer .arrow a.prev {
            left: .25rem;
            background-image: url(image/arrow-1.png);
            background-size: contain;
        }

        .memorabilia-layer .arrow a.next {
            right: .25rem;
            background-image: url(image/arrow-2.png);
            background-size: contain;
        }

        .memorabilia-layer .container {
            padding: 0 .8rem 1.25rem .8rem;
        }

        .memorabilia-layer .container .imgs {
            left: 0.1rem;
            z-index: 1;
        }

        .memorabilia-layer .container .imgs .swiper-slide {
            text-align: center;
        }

        .memorabilia-layer .container .imgs img {
            object-fit: contain;
            width: 100%;
            height: 2rem;
            object-position: 50% 50%;
        }

        .memorabilia-layer .container .imgs .dots {
            padding-top: .12rem;
        }

        .memorabilia-layer .container .imgs .dots span {
            width: .17rem;
            height: .17rem;
            color: #fff;
            text-align: center;
            line-height: .16rem;
            font-size: .12rem;
            cursor: pointer;
        }


        @media screen and (max-width: 800px) {
            .memorabilia-layer .container {
                padding: 0 .8rem 1.25rem .8rem;
            }

            .memorabilia-layer .container .imgs {
                left: 0;
                width: auto;
            }

            .memorabilia-layer .content {
                max-height: 3rem;
                margin-bottom: .3rem;
            }

            .memorabilia-layer .container .imgs img {
                height: 3rem;
            }


        }

        .memorabilia-layer .item {
            width: 100%;
        }

        .memorabilia-layer .container .imgs {
            display: inline-block;
            left: .2rem;
        }

        .memorabilia-layer .container .content {
            display: inline-block;
            vertical-align: top;
        }
    </style>
</body>

</html>