/* Poly Heart model by Q<PERSON><PERSON>ius [CC0] (https://creativecommons.org/publicdomain/zero/1.0/) via Poly Pizza (https://poly.pizza/m/1yCRUwFnwX)
 */

import * as THREE from "https://cdn.skypack.dev/three@0.135.0";
import { gsap } from "https://cdn.skypack.dev/gsap@3.8.0";
import { GLTFLoader } from "https://cdn.skypack.dev/three@0.135.0/examples/jsm/loaders/GLTFLoader";
class World {
  constructor({
    canvas,
    width,
    height,
    cameraPosition,
    fieldOfView = 75,
    nearPlane = 0.1,
    farPlane = 100
  }) {
    this.parameters = {
      count: 1500,
      max: 12.5 * Math.PI,
      a: 2,
      c: 4.5
    };
    this.textureLoader = new THREE.TextureLoader();
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x16000a);
    this.clock = new THREE.Clock();
    this.data = 0;
    this.time = { current: 0, t0: 0, t1: 0, t: 0, frequency: 0.0005 };
    this.angle = { x: 0, z: 0 };
    this.width = width || window.innerWidth;
    this.height = height || window.innerHeight;
    this.aspectRatio = this.width / this.height;
    this.fieldOfView = fieldOfView;
    this.camera = new THREE.PerspectiveCamera(
      fieldOfView,
      this.aspectRatio,
      nearPlane,
      farPlane
    );
    this.camera.position.set(
      cameraPosition.x,
      cameraPosition.y,
      cameraPosition.z
    );
    this.scene.add(this.camera);
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: true
    });
    this.pixelRatio = Math.min(window.devicePixelRatio, 2);
    this.renderer.setPixelRatio(this.pixelRatio);
    this.renderer.setSize(this.width, this.height);
    this.timer = 0;
    this.addToScene();
    this.addButton();
    this.addMouseInteraction();
    this.initTimelineHint();

    this.render();
    this.listenToResize();
    this.listenToMouseMove();
  }
  start() {}
  render() {
    this.renderer.render(this.scene, this.camera);
    this.composer && this.composer.render();
  }
  loop() {
    this.time.elapsed = this.clock.getElapsedTime();
    this.time.delta = Math.min(
      60,
      (this.time.current - this.time.elapsed) * 1000
    );
    if (this.analyser && this.isRunning) {
      this.time.t = this.time.elapsed - this.time.t0 + this.time.t1;
      this.data = this.analyser.getAverageFrequency();
      this.data *= this.data / 2000;
      this.angle.x += this.time.delta * 0.001 * 0.63;
      this.angle.z += this.time.delta * 0.001 * 0.39;
      const justFinished = this.isRunning && !this.sound.isPlaying;
      if (justFinished) {
        this.time.t1 = this.time.t;
        this.audioBtn.disabled = false;
        this.isRunning = false;
        const tl = gsap.timeline();
        this.angle.x = 0;
        this.angle.z = 0;
        tl.to(this.camera.position, {
          x: 0,
          z: 4.5,
          duration: 4,
          ease: "expo.in"
        });
        tl.to(this.audioBtn, {
          opacity: () => 1,
          duration: 1,
          ease: "power1.out"
        });
      } else {
        this.camera.position.x = Math.sin(this.angle.x) * this.parameters.a;
        this.camera.position.z = Math.min(
          Math.max(Math.cos(this.angle.z) * this.parameters.c, 1.75),
          6.5
        );
      }
    }
    this.camera.lookAt(this.scene.position);
    if (this.heartMaterial) {
      this.heartMaterial.uniforms.uTime.value +=
        this.time.delta * this.time.frequency * (1 + this.data * 0.2);
    }
    if (this.model) {
      this.model.rotation.y -= 0.0005 * this.time.delta * (1 + this.data);
    }
    if (this.snowMaterial) {
      this.snowMaterial.uniforms.uTime.value +=
        this.time.delta * 0.0004 * (1 + this.data);
    }
    
    // 时间线系统动画更新 - 参考 test.html 的 myrender 函数
    this.updateTimelineSystem();

    // 背景图片动画 - 优化后的旋转和移动
    if (this.backgroundGroup) {
      this.backgroundGroup.children.forEach((mesh) => {
        if (mesh.userData.rotationSpeed) {
          // 基础旋转速度 - 更温和
          let speedMultiplier = 0.3;

          // 音乐响应
          if (this.analyser && this.isRunning && this.data) {
            speedMultiplier = 0.3 + this.data * 0.1;
          }

          // 轻微旋转动画
          mesh.rotation.x += mesh.userData.rotationSpeed.x * this.time.delta * speedMultiplier;
          mesh.rotation.y += mesh.userData.rotationSpeed.y * this.time.delta * speedMultiplier;
          mesh.rotation.z += mesh.userData.rotationSpeed.z * this.time.delta * speedMultiplier;

          // 限制旋转角度
          mesh.rotation.x = Math.max(-0.05, Math.min(0.05, mesh.rotation.x));
          mesh.rotation.y = Math.max(-0.05, Math.min(0.05, mesh.rotation.y));
          mesh.rotation.z = Math.max(-0.02, Math.min(0.02, mesh.rotation.z));

          // 保持面向相机
          mesh.lookAt(this.camera.position);
        }
      });
    }
    
    this.render();

    this.time.current = this.time.elapsed;
    requestAnimationFrame(this.loop.bind(this));
  }
  listenToResize() {
    window.addEventListener("resize", () => {
      // Update sizes
      this.width = window.innerWidth;
      this.height = window.innerHeight;

      // Update camera
      this.camera.aspect = this.width / this.height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(this.width, this.height);
    });
  }
  listenToMouseMove() {
    window.addEventListener("mousemove", (e) => {
      const x = e.clientX;
      const y = e.clientY;
      gsap.to(this.camera.position, {
        x: gsap.utils.mapRange(0, window.innerWidth, 0.2, -0.2, x),
        y: gsap.utils.mapRange(0, window.innerHeight, 0.2, -0.2, -y)
      });
    });
  }
  
  // 时间线交互系统 - 参考 test.html 的交互逻辑
  addMouseInteraction() {
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    // 点击事件 - 参考 test.html 的 onMouseDown 和 openpop 函数
    window.addEventListener('click', (event) => {
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      raycaster.setFromCamera(mouse, this.camera);

      // 检测时间线图片点击
      const imageIntersects = raycaster.intersectObjects(this.backgroundGroup ? this.backgroundGroup.children : []);
      // 检测时间线标签点击
      const labelIntersects = raycaster.intersectObjects(this.labelGroup ? this.labelGroup.children : []);

      console.log('点击检测:', { imageIntersects: imageIntersects.length, labelIntersects: labelIntersects.length });

      if (imageIntersects.length > 0 || labelIntersects.length > 0) {
        const clickedObject = imageIntersects.length > 0 ?
          imageIntersects[0].object : labelIntersects[0].object;
        const timelineData = clickedObject.userData.timelineData;
        const index = clickedObject.userData.index;

        console.log(`时间线点击: ${timelineData.year} - ${timelineData.title}`);

        // 触发时间线弹窗 - 参考 test.html 的 openpop 函数
        this.openTimelinePopup(timelineData, index);

        // 点击动画效果
        if (imageIntersects.length > 0) {
          const clickedMesh = imageIntersects[0].object;
          this.animateTimelineClick(clickedMesh);
        }

        // 停止时间线自动移动
        this.timelineProgress.speed = 0;
      } else {
        // 点击空白区域，恢复时间线移动
        this.timelineProgress.speed = 0.001;
      }
    });

    // 鼠标移动事件 - 参考 test.html 的 onCursorMove 和 onPointerMove
    window.addEventListener('mousemove', (event) => {
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      raycaster.setFromCamera(mouse, this.camera);
      const imageIntersects = raycaster.intersectObjects(this.backgroundGroup.children);
      const labelIntersects = raycaster.intersectObjects(this.labelGroup.children);

      // 重置所有图片透明度
      this.backgroundGroup.children.forEach((mesh) => {
        if (mesh.material) {
          mesh.material.opacity = this.timelineConfig.opacity;
        }
      });

      // 高亮悬停的时间线元素
      if (imageIntersects.length > 0 || labelIntersects.length > 0) {
        document.body.style.cursor = 'pointer';

        if (imageIntersects.length > 0) {
          const hoveredMesh = imageIntersects[0].object;
          hoveredMesh.material.opacity = this.timelineConfig.opacity * 1.3;
        }
      } else {
        document.body.style.cursor = 'default';
      }

      // 时间线导航控制 - 参考 test.html 的鼠标位置控制
      this.updateTimelineNavigation(event);
    });

    // 滚轮事件 - 参考 test.html 的 onMouseScroll
    window.addEventListener('wheel', (event) => {
      event.preventDefault();
      this.handleTimelineScroll(event);
    }, { passive: false });
  }

  // 时间线导航更新 - 参考 test.html 的 onPointerMove 逻辑
  updateTimelineNavigation(event) {
    if (!this.timelineProgress) return;

    const centerY = window.innerHeight / 2;
    const deltaY = (event.clientY - centerY) / (window.innerHeight / 2);

    // 根据鼠标位置调整时间线移动速度
    if (Math.abs(deltaY) > 0.1) {
      this.timelineProgress.speed = deltaY * 0.0005;
    } else {
      this.timelineProgress.speed = 0.0001; // 默认缓慢移动
    }
  }

  // 时间线滚轮控制 - 参考 test.html 的滚轮逻辑
  handleTimelineScroll(event) {
    if (!this.timelineProgress) return;

    const delta = event.deltaY > 0 ? 1 : -1;
    const scrollSpeed = 0.02;

    this.timelineProgress.target += delta * scrollSpeed;
    this.timelineProgress.target = Math.max(
      this.timelineProgress.min,
      Math.min(this.timelineProgress.max, this.timelineProgress.target)
    );

    console.log(`时间线滚动: ${(this.timelineProgress.target * 100).toFixed(1)}%`);
  }

  // 时间线点击动画
  animateTimelineClick(mesh) {
    const originalScale = mesh.userData.scale;

    gsap.to(mesh.scale, {
      x: originalScale * 1.2,
      y: originalScale * 1.2,
      z: originalScale * 1.2,
      duration: 0.2,
      ease: "power2.out",
      onComplete: () => {
        gsap.to(mesh.scale, {
          x: originalScale,
          y: originalScale,
          z: originalScale,
          duration: 0.3,
          ease: "elastic.out(1, 0.5)"
        });
      }
    });
  }

  // 时间线弹窗系统 - 参考 test.html 的 openpop 函数
  openTimelinePopup(timelineData, index) {
    // 创建或更新弹窗内容
    this.showTimelinePopup(timelineData);

    // 相机动画 - 聚焦到选中的时间点
    const targetProgress = (index / (this.timelineData.length - 1)) * 0.8 + 0.1;
    this.timelineProgress.target = targetProgress;

    // 相机移动动画
    const targetPoint = this.timelineCurve.getPointAt(targetProgress);
    gsap.to(this.camera.position, {
      x: targetPoint.x * 0.001,
      y: targetPoint.y * 0.001,
      z: targetPoint.z * 0.001 + 4.5,
      duration: 2,
      ease: "power2.inOut"
    });
  }

  // 显示时间线弹窗
  showTimelinePopup(timelineData) {
    // 移除现有弹窗
    const existingPopup = document.getElementById('timeline-popup');
    if (existingPopup) {
      existingPopup.remove();
    }

    // 创建新弹窗
    const popup = document.createElement('div');
    popup.id = 'timeline-popup';
    popup.innerHTML = `
      <div class="timeline-popup-content">
        <button class="timeline-popup-close">&times;</button>
        <h2>${timelineData.year}</h2>
        <h3>${timelineData.title}</h3>
        <div class="timeline-popup-image">
          <img src="${timelineData.image}" alt="${timelineData.title}" />
        </div>
        <p>这是 ${timelineData.year} 年的美好回忆...</p>
      </div>
    `;

    document.body.appendChild(popup);

    // 弹窗动画
    gsap.fromTo(popup,
      { opacity: 0, scale: 0.8 },
      { opacity: 1, scale: 1, duration: 0.3, ease: "power2.out" }
    );

    // 关闭按钮事件
    popup.querySelector('.timeline-popup-close').addEventListener('click', () => {
      this.closeTimelinePopup();
    });

    // 点击背景关闭
    popup.addEventListener('click', (e) => {
      if (e.target === popup) {
        this.closeTimelinePopup();
      }
    });
  }

  // 关闭时间线弹窗
  closeTimelinePopup() {
    const popup = document.getElementById('timeline-popup');
    if (popup) {
      gsap.to(popup, {
        opacity: 0,
        scale: 0.8,
        duration: 0.2,
        ease: "power2.in",
        onComplete: () => {
          popup.remove();
        }
      });
    }

    // 恢复相机位置
    gsap.to(this.camera.position, {
      x: 0,
      y: 0,
      z: 4.5,
      duration: 1.5,
      ease: "power2.inOut"
    });

    // 恢复时间线移动
    this.timelineProgress.speed = 0.001;
  }

  // 时间线系统更新 - 参考 test.html 的时间线进度控制
  updateTimelineSystem() {
    if (!this.timelineProgress || !this.timelineCurve) return;

    // 更新时间线进度 - 参考 test.html 的 playpercentage 逻辑
    if (this.timelineProgress.speed !== 0) {
      this.timelineProgress.current += this.timelineProgress.speed * this.time.delta;

      // 边界检查
      if (this.timelineProgress.current >= this.timelineProgress.max) {
        this.timelineProgress.current = this.timelineProgress.max;
        this.timelineProgress.speed = -Math.abs(this.timelineProgress.speed);
      } else if (this.timelineProgress.current <= this.timelineProgress.min) {
        this.timelineProgress.current = this.timelineProgress.min;
        this.timelineProgress.speed = Math.abs(this.timelineProgress.speed);
      }
    }

    // 平滑过渡到目标进度
    if (Math.abs(this.timelineProgress.target - this.timelineProgress.current) > 0.001) {
      this.timelineProgress.current +=
        (this.timelineProgress.target - this.timelineProgress.current) * 0.02;
    }

    // 更新相机位置沿时间线移动 - 参考 test.html 的相机跟随逻辑
    this.updateTimelineCamera();

    // 更新时间线元素的可见性和动画
    this.updateTimelineElements();
  }

  // 更新时间线相机 - 参考 test.html 的相机控制
  updateTimelineCamera() {
    if (!this.timelineCurve || !this.timelineProgress) return;

    const t = this.timelineProgress.current;
    const position = new THREE.Vector3();
    const lookAt = new THREE.Vector3();
    const direction = new THREE.Vector3();

    // 获取当前位置和方向
    this.timelineCurve.getPointAt(t, position);
    this.timelineCurve.getTangentAt(t, direction);

    // 计算相机偏移 - 参考 test.html 的偏移计算
    const offset = 15;
    const normal = new THREE.Vector3(0, 1, 0);
    const binormal = new THREE.Vector3().crossVectors(direction, normal);

    // 设置相机位置（在时间线旁边）
    position.add(binormal.clone().multiplyScalar(offset));
    position.add(normal.clone().multiplyScalar(offset * 0.5));

    // 缩放位置到合适的相机距离
    position.multiplyScalar(0.01);
    position.z += 4.5;

    // 平滑相机移动
    if (!this.isRunning) { // 只在非音乐播放时移动相机
      this.camera.position.lerp(position, 0.01);
    }

    // 计算前方注视点
    this.timelineCurve.getPointAt(Math.min(t + 0.1, 1), lookAt);
    lookAt.multiplyScalar(0.01);

    // 让相机看向时间线前方
    if (!this.isRunning) {
      this.camera.lookAt(lookAt);
    }
  }

  // 更新时间线元素 - 参考 test.html 的元素更新逻辑
  updateTimelineElements() {
    if (!this.backgroundGroup || !this.labelGroup || !this.timelineProgress) return;

    const currentProgress = this.timelineProgress.current;

    // 更新图片透明度和缩放 - 基于距离当前进度的远近
    this.backgroundGroup.children.forEach((mesh, index) => {
      const elementProgress = (index / (this.backgroundGroup.children.length - 1)) * 0.8 + 0.1;
      const distance = Math.abs(elementProgress - currentProgress);

      // 距离越近，透明度越高，缩放越大
      const maxDistance = 0.2;
      const normalizedDistance = Math.min(distance / maxDistance, 1);
      const opacity = this.timelineConfig.opacity * (1.5 - normalizedDistance);
      const scale = this.timelineConfig.scale * (1.2 - normalizedDistance * 0.3);

      if (mesh.material) {
        mesh.material.opacity = Math.max(0.2, opacity);
      }

      // 平滑缩放变化
      const targetScale = Math.max(0.3, scale);
      mesh.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.05);
    });

    // 更新标签透明度
    this.labelGroup.children.forEach((label, index) => {
      const elementProgress = (index / (this.labelGroup.children.length - 1)) * 0.8 + 0.1;
      const distance = Math.abs(elementProgress - currentProgress);
      const maxDistance = 0.15;
      const normalizedDistance = Math.min(distance / maxDistance, 1);
      const opacity = 1 - normalizedDistance * 0.7;

      if (label.material) {
        label.material.opacity = Math.max(0.3, opacity);
      }
    });
  }
  addHeart() {
    this.heartMaterial = new THREE.ShaderMaterial({
      fragmentShader: document.getElementById("fragmentShader").textContent,
      vertexShader: document.getElementById("vertexShader").textContent,
      uniforms: {
        uTime: { value: 0 },
        uSize: { value: 0.2 },
        uTex: {
          value: new THREE.TextureLoader().load(
            "https://assets.codepen.io/74321/heart.png"
          )
        }
      },
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    const count = this.parameters.count; //2000
    const scales = new Float32Array(count * 1);
    const colors = new Float32Array(count * 3);
    const speeds = new Float32Array(count);
    const randoms = new Float32Array(count);
    const randoms1 = new Float32Array(count);
    const colorChoices = [
      "white",
      "red",
      "pink",
      "crimson",
      "hotpink",
      "green"
    ];

    const squareGeometry = new THREE.PlaneGeometry(1, 1);
    this.instancedGeometry = new THREE.InstancedBufferGeometry();
    Object.keys(squareGeometry.attributes).forEach((attr) => {
      this.instancedGeometry.attributes[attr] = squareGeometry.attributes[attr];
    });
    this.instancedGeometry.index = squareGeometry.index;
    this.instancedGeometry.maxInstancedCount = count;

    for (let i = 0; i < count; i++) {
      const phi = Math.random() * Math.PI * 2;
      const i3 = 3 * i;
      randoms[i] = Math.random();
      randoms1[i] = Math.random();
      scales[i] = Math.random() * 0.35;
      const colorIndex = Math.floor(Math.random() * colorChoices.length);
      const color = new THREE.Color(colorChoices[colorIndex]);
      colors[i3 + 0] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
      speeds[i] = Math.random() * this.parameters.max;
    }
    this.instancedGeometry.setAttribute(
      "random",
      new THREE.InstancedBufferAttribute(randoms, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random1",
      new THREE.InstancedBufferAttribute(randoms1, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aScale",
      new THREE.InstancedBufferAttribute(scales, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aSpeed",
      new THREE.InstancedBufferAttribute(speeds, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aColor",
      new THREE.InstancedBufferAttribute(colors, 3, false)
    );
    this.heart = new THREE.Mesh(this.instancedGeometry, this.heartMaterial);
    console.log(this.heart);
    this.scene.add(this.heart);
  }
  addToScene() {
    this.addBackgroundImages();
    this.addModel();
    this.addHeart();
    this.addSnow();
  }
  async addModel() {
    this.model = await this.loadObj(
      "https://assets.codepen.io/74321/heart.glb"
    );
    this.model.scale.set(0.01, 0.01, 0.01);
    this.model.material = new THREE.MeshMatcapMaterial({
      matcap: this.textureLoader.load(
        "https://assets.codepen.io/74321/3.png",
        () => {
          gsap.to(this.model.scale, {
            x: 0.35,
            y: 0.35,
            z: 0.35,
            duration: 1.5,
            ease: "Elastic.easeOut"
          });
        }
      ),
      color: "#ff89aC"
    });
    this.scene.add(this.model);
  }
  addButton() {
    this.audioBtn = document.querySelector("button");
    this.audioBtn.addEventListener("click", () => {
      this.audioBtn.disabled = true;
      if (this.analyser) {
        this.sound.play();
        this.time.t0 = this.time.elapsed;
        this.data = 0;
        this.isRunning = true;
        gsap.to(this.audioBtn, {
          opacity: 0,
          duration: 1,
          ease: "power1.out"
        });
      } else {
        this.loadMusic().then(() => {
          console.log("music loaded");
        });
      }
    });
  }
  loadObj(path) {
    const loader = new GLTFLoader();
    return new Promise((resolve) => {
      loader.load(
        path,
        (response) => {
          resolve(response.scene.children[0]);
        },
        (xhr) => {},
        (err) => {
          console.log(err);
        }
      );
    });
  }
  loadMusic() {
    return new Promise((resolve) => {
      const listener = new THREE.AudioListener();
      this.camera.add(listener);
      // create a global audio source
      this.sound = new THREE.Audio(listener);
      const audioLoader = new THREE.AudioLoader();
      audioLoader.load(
        // "https://assets.codepen.io/74321/ukulele.mp3",
        "./很爱很爱你.mp3",
        (buffer) => {
          this.sound.setBuffer(buffer);
          this.sound.setLoop(false);
          this.sound.setVolume(0.5);
          this.sound.play();
          this.analyser = new THREE.AudioAnalyser(this.sound, 32);
          // get the average frequency of the sound
          const data = this.analyser.getAverageFrequency();
          this.isRunning = true;
          this.t0 = this.time.elapsed;
          resolve(data);
        },
        (progress) => {
          gsap.to(this.audioBtn, {
            opacity: () => 1 - progress.loaded / progress.total,
            duration: 1,
            ease: "power1.out"
          });
        },

        (error) => {
          console.log(error);
        }
      );
    });
  }
  addSnow() {
    this.snowMaterial = new THREE.ShaderMaterial({
      fragmentShader: document.getElementById("fragmentShader1").textContent,
      vertexShader: document.getElementById("vertexShader1").textContent,
      uniforms: {
        uTime: { value: 0 },
        uSize: { value: 0.3 },
        uTex: {
          value: new THREE.TextureLoader().load(
            "https://assets.codepen.io/74321/heart.png"
          )
        }
      },
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    const count = 550;
    const scales = new Float32Array(count * 1);
    const colors = new Float32Array(count * 3);
    const phis = new Float32Array(count);
    const randoms = new Float32Array(count);
    const randoms1 = new Float32Array(count);
    const colorChoices = ["red", "pink", "hotpink", "green"];

    const squareGeometry = new THREE.PlaneGeometry(1, 1);
    this.instancedGeometry = new THREE.InstancedBufferGeometry();
    Object.keys(squareGeometry.attributes).forEach((attr) => {
      this.instancedGeometry.attributes[attr] = squareGeometry.attributes[attr];
    });
    this.instancedGeometry.index = squareGeometry.index;
    this.instancedGeometry.maxInstancedCount = count;

    for (let i = 0; i < count; i++) {
      const phi = (Math.random() - 0.5) * 10;
      const i3 = 3 * i;
      phis[i] = phi;
      randoms[i] = Math.random();
      randoms1[i] = Math.random();
      scales[i] = Math.random() * 0.35;
      const colorIndex = Math.floor(Math.random() * colorChoices.length);
      const color = new THREE.Color(colorChoices[colorIndex]);
      colors[i3 + 0] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }
    this.instancedGeometry.setAttribute(
      "phi",
      new THREE.InstancedBufferAttribute(phis, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random",
      new THREE.InstancedBufferAttribute(randoms, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random1",
      new THREE.InstancedBufferAttribute(randoms1, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aScale",
      new THREE.InstancedBufferAttribute(scales, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aColor",
      new THREE.InstancedBufferAttribute(colors, 3, false)
    );
    this.snow = new THREE.Mesh(this.instancedGeometry, this.snowMaterial);
    this.scene.add(this.snow);
  }
  
  addBackgroundImages() {
    // 时间线配置 - 确保图片可见
    const timelineConfig = {
      imageSize: 2, // 适中的图片大小
      labelSize: 20, // 标签大小
      depth: 0, // 不设置深度偏移
      rotationSpeed: 0.0002, // 旋转速度
      opacity: 1.0, // 完全不透明
      scale: 1.0, // 正常缩放
      curveScale: 0.5 // 曲线缩放
    };

    // 创建背景图片组和标签组
    this.backgroundGroup = new THREE.Group();
    this.labelGroup = new THREE.Group();
    this.scene.add(this.backgroundGroup);
    this.scene.add(this.labelGroup);

    // 时间线数据 - 参考 test.html 的年份数组概念
    const timelineData = [
      { year: "2020", image: "assets/images/bg1.svg", title: "初遇" },
      { year: "2021", image: "assets/images/bg2.svg", title: "相识" },
      { year: "2022", image: "assets/images/bg3.svg", title: "相知" },
      { year: "2023", image: "assets/images/bg4.svg", title: "相恋" },
      { year: "2024", image: "assets/images/bg5.svg", title: "相守" },
      { year: "2025", image: "assets/images/bg6.svg", title: "未来" }
    ];

    // 创建简化的时间线曲线 - 在相机可见范围内
    const timelineCurve = new THREE.CatmullRomCurve3([
      new THREE.Vector3(-4, 1, -2),
      new THREE.Vector3(-2, 0.5, -1),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(2, -0.5, 1),
      new THREE.Vector3(4, -1, 2),
      new THREE.Vector3(6, -0.5, 3)
    ]);

    // 创建简单的线性排列作为备选
    this.createSimpleTimelineLayout(timelineData, timelineConfig);

    // 时间线进度控制 - 参考 test.html 的百分比系统
    this.timelineProgress = {
      current: 0.1, // 当前进度 (0-1)
      target: 0.1,  // 目标进度
      speed: 0.001, // 移动速度
      min: 0.05,    // 最小进度
      max: 0.95     // 最大进度
    };

    // 沿着时间线曲线放置图片和标签
    const totalItems = timelineData.length;
    for (let i = 0; i < totalItems; i++) {
      const t = i / (totalItems - 1); // 简化分布，使用整个曲线
      const point = timelineCurve.getPointAt(t);
      const data = timelineData[i];

      // 创建图片
      this.createTimelineImage(point, data, i, totalItems, timelineConfig);

      // 创建年份标签 - 参考 test.html 的 makeLabel 函数
      this.createTimelineLabel(point, data, i, timelineConfig);
    }

    // 添加一个测试图片，确保图片系统工作
    this.createTestImage(timelineConfig);

    // 存储时间线引用
    this.timelineCurve = timelineCurve;
    this.timelineConfig = timelineConfig;
    this.timelineData = timelineData;

    console.log(`时间线系统创建完成: ${totalItems} 个时间点`);
    console.log('时间线进度:', this.timelineProgress);
    console.log('背景图片组:', this.backgroundGroup.children.length);
    console.log('标签组:', this.labelGroup.children.length);
  }

  // 创建时间线图片
  createTimelineImage(point, data, index, total, config) {
    const texture = this.textureLoader.load(
      data.image,
      (texture) => {
        console.log(`时间线图片加载成功: ${data.year} - ${data.title}`);
        // 图片加载成功后，确保材质更新
        mesh.material.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn(`时间线图片加载失败: ${data.image}`, error);
      }
    );

    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide,
      alphaTest: 0.1 // 添加 alpha 测试
    });

    const geometry = new THREE.PlaneGeometry(config.imageSize, config.imageSize);
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置 - 确保在可见范围内
    mesh.position.copy(point);
    mesh.position.z += config.depth;
    mesh.scale.setScalar(config.scale);

    // 让图片面向相机
    mesh.lookAt(this.camera.position);

    // 存储时间线数据
    mesh.userData = {
      timelineData: data,
      index: index,
      total: total,
      originalPosition: point.clone(),
      scale: config.scale,
      rotationSpeed: {
        x: (Math.random() - 0.5) * config.rotationSpeed,
        y: (Math.random() - 0.5) * config.rotationSpeed,
        z: (Math.random() - 0.5) * config.rotationSpeed * 0.5
      }
    };

    this.backgroundGroup.add(mesh);

    // 调试信息
    console.log(`创建时间线图片 ${index}: 位置 (${point.x.toFixed(2)}, ${point.y.toFixed(2)}, ${point.z.toFixed(2)})`);
  }

  // 创建时间线标签 - 参考 test.html 的 makeLabel 函数
  createTimelineLabel(point, data, index, config) {
    const canvas = this.makeLabelCanvas(120, 24, data.year, 'rgba(0,0,0,0.7)', 'white');
    const texture = new THREE.CanvasTexture(canvas);
    texture.minFilter = THREE.LinearFilter;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    const labelMaterial = new THREE.SpriteMaterial({
      map: texture,
      transparent: true,
    });

    const labelBaseScale = 0.05; // 增大标签缩放
    const label = new THREE.Sprite(labelMaterial);
    label.position.copy(point);
    label.position.y -= 1; // 减小偏移，让标签更靠近图片
    label.position.z += config.depth + 1; // 稍微前移
    label.scale.set(
      canvas.width * labelBaseScale,
      canvas.height * labelBaseScale,
      1
    );

    // 存储标签数据
    label.userData = {
      timelineData: data,
      index: index,
      type: 'label'
    };

    this.labelGroup.add(label);

    // 调试信息
    console.log(`创建时间线标签 ${index}: ${data.year} 位置 (${label.position.x.toFixed(2)}, ${label.position.y.toFixed(2)}, ${label.position.z.toFixed(2)})`);
  }

  // 创建测试图片，确保图片系统工作
  createTestImage(config) {
    // 创建一个简单的测试纹理
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    // 绘制一个彩色方块
    ctx.fillStyle = '#ff89aC';
    ctx.fillRect(0, 0, 64, 64);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('TEST', 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.9
    });

    const geometry = new THREE.PlaneGeometry(2, 2);
    const mesh = new THREE.Mesh(geometry, material);

    // 放置在相机前面，确保可见
    mesh.position.set(0, 0, -3);
    mesh.lookAt(this.camera.position);

    this.backgroundGroup.add(mesh);
    console.log('测试图片已创建，位置:', mesh.position);
  }

  // 创建简单的线性时间线布局
  createSimpleTimelineLayout(timelineData, config) {
    console.log('创建简单时间线布局...');

    timelineData.forEach((data, index) => {
      // 简单的线性排列
      const x = (index - timelineData.length / 2) * 2; // 水平间距2单位
      const y = Math.sin(index * 0.5) * 0.5; // 轻微的波浪形
      const z = -3; // 固定深度

      // 创建图片
      const texture = this.textureLoader.load(
        data.image,
        (loadedTexture) => {
          console.log(`简单布局图片加载成功: ${data.year}`);
        },
        undefined,
        (error) => {
          console.warn(`简单布局图片加载失败: ${data.image}`, error);
          // 创建备用颜色方块
          this.createColorBlock(x, y, z, data, index);
        }
      );

      const material = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        opacity: config.opacity,
        side: THREE.DoubleSide
      });

      const geometry = new THREE.PlaneGeometry(1.5, 1.5); // 固定大小，确保可见
      const mesh = new THREE.Mesh(geometry, material);

      mesh.position.set(x, y, z);
      mesh.lookAt(this.camera.position);

      mesh.userData = {
        timelineData: data,
        index: index,
        scale: config.scale
      };

      this.backgroundGroup.add(mesh);
      console.log(`简单布局图片 ${index}: ${data.year} 位置 (${x}, ${y}, ${z})`);
    });
  }

  // 创建颜色方块作为备用
  createColorBlock(x, y, z, data, index) {
    const colors = ['#ff89aC', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff6b6b'];
    const color = colors[index % colors.length];

    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    ctx.fillStyle = color;
    ctx.fillRect(0, 0, 64, 64);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(data.year, 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.8
    });

    const geometry = new THREE.PlaneGeometry(1, 1);
    const mesh = new THREE.Mesh(geometry, material);

    mesh.position.set(x, y, z);
    mesh.lookAt(this.camera.position);

    mesh.userData = {
      timelineData: data,
      index: index,
      scale: 1
    };

    this.backgroundGroup.add(mesh);
    console.log(`颜色方块 ${index}: ${data.year} 位置 (${x}, ${y}, ${z})`);
  }

  // 创建标签画布 - 参考 test.html 的 makeLabelCanvas 函数
  makeLabelCanvas(baseWidth, size, name, bkcolor, color) {
    const borderSize = 2;
    const ctx = document.createElement('canvas').getContext('2d');
    const font = `bold ${size}px Arial, sans-serif`;
    ctx.font = font;

    const textWidth = ctx.measureText(name).width;
    const doubleBorderSize = borderSize * 2;
    const width = Math.max(baseWidth, textWidth + doubleBorderSize);
    const height = size + doubleBorderSize;

    ctx.canvas.width = width;
    ctx.canvas.height = height;

    // 重新设置字体
    ctx.font = font;
    ctx.textBaseline = 'middle';
    ctx.textAlign = 'center';

    // 背景
    ctx.fillStyle = bkcolor;
    ctx.fillRect(0, 0, width, height);

    // 文字
    const scaleFactor = Math.min(1, baseWidth / textWidth);
    ctx.translate(width / 2, height / 2);
    ctx.scale(scaleFactor, 1);
    ctx.fillStyle = color;
    ctx.fillText(name, 0, 0);

    return ctx.canvas;
  }

  // 初始化时间线提示
  initTimelineHint() {
    const hint = document.getElementById('timeline-hint');
    if (hint) {
      // 5秒后自动隐藏提示
      setTimeout(() => {
        gsap.to(hint, {
          opacity: 0,
          y: 20,
          duration: 0.5,
          ease: "power2.in",
          onComplete: () => {
            hint.style.display = 'none';
          }
        });
      }, 5000);

      // 点击提示隐藏
      hint.addEventListener('click', () => {
        gsap.to(hint, {
          opacity: 0,
          y: 20,
          duration: 0.3,
          ease: "power2.in",
          onComplete: () => {
            hint.style.display = 'none';
          }
        });
      });
    }
  }
}

const world = new World({
  canvas: document.querySelector("canvas.webgl"),
  cameraPosition: { x: 0, y: 0, z: 4.5 }
});

world.loop();
