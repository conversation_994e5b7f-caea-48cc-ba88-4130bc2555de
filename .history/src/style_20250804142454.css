* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  overflow: hidden;
  background: #16000a;
}
body {
  -webkit-font-smoothing: antialiased;
  color: #ffdada;
}
.webgl {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  outline: none;
}

body::before {
  content: "";
  position: absolute;
  border: 8px solid;
  inset: 1rem;
  z-index: 100;
  pointer-events: none;
}

h1 {
  position: absolute;
  top: 10vh;
  left: 2.5rem;
  right: 1rem;
  text-align: center;
  font-family: ador-hairline, sans-serif;
  font-weight: 900;
  font-size: max(1rem, 3vh);
}

button {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  height: 12vh;
  width: 12vh;
  transform: translateY(2vh);
  right: 0;
  margin: auto;
  -webkit-appearance: none;
  background: transparent;
  color: inherit;
  border: none;
  cursor: pointer;
}

svg {
  width: 3.5vh;
}

button:hover {
  opacity: 0.8;
}

/* 时间线弹窗样式 - 参考 test.html 的弹窗设计 */
#timeline-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.timeline-popup-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.timeline-popup-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.timeline-popup-close:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.timeline-popup-content h2 {
  color: #ff89aC;
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  text-align: center;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.timeline-popup-content h3 {
  color: white;
  font-size: 1.5rem;
  margin: 0 0 1.5rem 0;
  text-align: center;
  font-weight: 300;
  opacity: 0.9;
}

.timeline-popup-image {
  width: 100%;
  height: 200px;
  margin: 1.5rem 0;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.timeline-popup-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.timeline-popup-image:hover img {
  transform: scale(1.05);
}

.timeline-popup-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: center;
  margin: 1.5rem 0 0 0;
}
