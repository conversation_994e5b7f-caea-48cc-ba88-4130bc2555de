/* Poly Heart model by Q<PERSON><PERSON>ius [CC0] (https://creativecommons.org/publicdomain/zero/1.0/) via Poly Pizza (https://poly.pizza/m/1yCRUwFnwX)
 */

import * as THREE from "https://cdn.skypack.dev/three@0.135.0";
import { gsap } from "https://cdn.skypack.dev/gsap@3.8.0";
import { GLTFLoader } from "https://cdn.skypack.dev/three@0.135.0/examples/jsm/loaders/GLTFLoader";
class World {
  constructor({
    canvas,
    width,
    height,
    cameraPosition,
    fieldOfView = 75,
    nearPlane = 0.1,
    farPlane = 100
  }) {
    this.parameters = {
      count: 1500,
      max: 12.5 * Math.PI,
      a: 2,
      c: 4.5
    };
    this.textureLoader = new THREE.TextureLoader();
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x16000a);
    this.clock = new THREE.Clock();
    this.data = 0;
    this.time = { current: 0, t0: 0, t1: 0, t: 0, frequency: 0.0005 };
    this.angle = { x: 0, z: 0 };
    this.width = width || window.innerWidth;
    this.height = height || window.innerHeight;
    this.aspectRatio = this.width / this.height;
    this.fieldOfView = fieldOfView;
    this.camera = new THREE.PerspectiveCamera(
      fieldOfView,
      this.aspectRatio,
      nearPlane,
      farPlane
    );
    this.camera.position.set(
      cameraPosition.x,
      cameraPosition.y,
      cameraPosition.z
    );
    this.scene.add(this.camera);
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: true
    });
    this.pixelRatio = Math.min(window.devicePixelRatio, 2);
    this.renderer.setPixelRatio(this.pixelRatio);
    this.renderer.setSize(this.width, this.height);
    this.timer = 0;
    this.addToScene();
    this.addButton();
    this.addMouseInteraction();
    this.initTimelineHint();

    this.render();
    this.listenToResize();
    this.listenToMouseMove();
  }
  start() {}
  render() {
    this.renderer.render(this.scene, this.camera);
    this.composer && this.composer.render();
  }
  loop() {
    this.time.elapsed = this.clock.getElapsedTime();
    this.time.delta = Math.min(
      60,
      (this.time.current - this.time.elapsed) * 1000
    );
    if (this.analyser && this.isRunning) {
      this.time.t = this.time.elapsed - this.time.t0 + this.time.t1;
      this.data = this.analyser.getAverageFrequency();
      this.data *= this.data / 2000;
      this.angle.x += this.time.delta * 0.001 * 0.63;
      this.angle.z += this.time.delta * 0.001 * 0.39;
      const justFinished = this.isRunning && !this.sound.isPlaying;
      if (justFinished) {
        this.time.t1 = this.time.t;
        this.audioBtn.disabled = false;
        this.isRunning = false;
        const tl = gsap.timeline();
        this.angle.x = 0;
        this.angle.z = 0;
        tl.to(this.camera.position, {
          x: 0,
          z: 4.5,
          duration: 4,
          ease: "expo.in"
        });
        tl.to(this.audioBtn, {
          opacity: () => 1,
          duration: 1,
          ease: "power1.out"
        });
      } else {
        this.camera.position.x = Math.sin(this.angle.x) * this.parameters.a;
        this.camera.position.z = Math.min(
          Math.max(Math.cos(this.angle.z) * this.parameters.c, 1.75),
          6.5
        );
      }
    }
    this.camera.lookAt(this.scene.position);
    if (this.heartMaterial) {
      this.heartMaterial.uniforms.uTime.value +=
        this.time.delta * this.time.frequency * (1 + this.data * 0.2);
    }
    if (this.model) {
      this.model.rotation.y -= 0.0005 * this.time.delta * (1 + this.data);
    }
    if (this.snowMaterial) {
      this.snowMaterial.uniforms.uTime.value +=
        this.time.delta * 0.0004 * (1 + this.data);
    }
    
    // 时间线系统动画更新 - 参考 test.html 的 myrender 函数
    this.updateTimelineSystem();

    // 背景图片动画 - 优化后的旋转和移动
    if (this.backgroundGroup) {
      this.backgroundGroup.children.forEach((mesh) => {
        if (mesh.userData.rotationSpeed) {
          // 基础旋转速度 - 更温和
          let speedMultiplier = 0.3;

          // 音乐响应
          if (this.analyser && this.isRunning && this.data) {
            speedMultiplier = 0.3 + this.data * 0.1;
          }

          // 轻微旋转动画
          mesh.rotation.x += mesh.userData.rotationSpeed.x * this.time.delta * speedMultiplier;
          mesh.rotation.y += mesh.userData.rotationSpeed.y * this.time.delta * speedMultiplier;
          mesh.rotation.z += mesh.userData.rotationSpeed.z * this.time.delta * speedMultiplier;

          // 限制旋转角度
          mesh.rotation.x = Math.max(-0.05, Math.min(0.05, mesh.rotation.x));
          mesh.rotation.y = Math.max(-0.05, Math.min(0.05, mesh.rotation.y));
          mesh.rotation.z = Math.max(-0.02, Math.min(0.02, mesh.rotation.z));

          // 保持面向相机
          mesh.lookAt(this.camera.position);
        }
      });
    }
    
    this.render();

    this.time.current = this.time.elapsed;
    requestAnimationFrame(this.loop.bind(this));
  }
  listenToResize() {
    window.addEventListener("resize", () => {
      // Update sizes
      this.width = window.innerWidth;
      this.height = window.innerHeight;

      // Update camera
      this.camera.aspect = this.width / this.height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(this.width, this.height);
    });
  }
  listenToMouseMove() {
    window.addEventListener("mousemove", (e) => {
      const x = e.clientX;
      const y = e.clientY;
      gsap.to(this.camera.position, {
        x: gsap.utils.mapRange(0, window.innerWidth, 0.2, -0.2, x),
        y: gsap.utils.mapRange(0, window.innerHeight, 0.2, -0.2, -y)
      });
    });
  }
  
  // 时间线交互系统 - 参考 test.html 的交互逻辑
  addMouseInteraction() {
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    // 点击事件 - 参考 test.html 的 onMouseDown 和 openpop 函数
    window.addEventListener('click', (event) => {
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      raycaster.setFromCamera(mouse, this.camera);

      // 检测时间线图片点击
      const imageIntersects = raycaster.intersectObjects(this.backgroundGroup ? this.backgroundGroup.children : []);
      // 检测时间线标签点击
      const labelIntersects = raycaster.intersectObjects(this.labelGroup ? this.labelGroup.children : []);

      console.log('点击检测:', { imageIntersects: imageIntersects.length, labelIntersects: labelIntersects.length });

      if (imageIntersects.length > 0 || labelIntersects.length > 0) {
        const clickedObject = imageIntersects.length > 0 ?
          imageIntersects[0].object : labelIntersects[0].object;
        const timelineData = clickedObject.userData.timelineData;
        const index = clickedObject.userData.index;

        console.log(`时间线点击: ${timelineData.year} - ${timelineData.title}`);

        // 触发时间线弹窗 - 参考 test.html 的 openpop 函数
        this.openTimelinePopup(timelineData, index);

        // 点击动画效果
        if (imageIntersects.length > 0) {
          const clickedMesh = imageIntersects[0].object;
          this.animateTimelineClick(clickedMesh);
        }

        // 停止时间线自动移动
        this.timelineProgress.speed = 0;
      } else {
        // 点击空白区域，恢复时间线移动
        this.timelineProgress.speed = 0.001;
      }
    });

    // 鼠标移动事件 - 参考 test.html 的 onCursorMove 和 onPointerMove
    window.addEventListener('mousemove', (event) => {
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      raycaster.setFromCamera(mouse, this.camera);
      const imageIntersects = raycaster.intersectObjects(this.backgroundGroup.children);
      const labelIntersects = raycaster.intersectObjects(this.labelGroup.children);

      // 重置所有图片透明度
      this.backgroundGroup.children.forEach((mesh) => {
        if (mesh.material) {
          mesh.material.opacity = this.timelineConfig.opacity;
        }
      });

      // 高亮悬停的时间线元素
      if (imageIntersects.length > 0 || labelIntersects.length > 0) {
        document.body.style.cursor = 'pointer';

        if (imageIntersects.length > 0) {
          const hoveredMesh = imageIntersects[0].object;
          hoveredMesh.material.opacity = this.timelineConfig.opacity * 1.3;
        }
      } else {
        document.body.style.cursor = 'default';
      }

      // 时间线导航控制 - 参考 test.html 的鼠标位置控制
      this.updateTimelineNavigation(event);
    });

    // 滚轮事件 - 参考 test.html 的 onMouseScroll
    window.addEventListener('wheel', (event) => {
      event.preventDefault();
      this.handleTimelineScroll(event);
    }, { passive: false });
  }

  // 时间线导航更新 - 参考 test.html 的 onPointerMove 逻辑
  updateTimelineNavigation(event) {
    if (!this.timelineProgress) return;

    const centerY = window.innerHeight / 2;
    const deltaY = (event.clientY - centerY) / (window.innerHeight / 2);

    // 根据鼠标位置调整时间线移动速度
    if (Math.abs(deltaY) > 0.1) {
      this.timelineProgress.speed = deltaY * 0.0005;
    } else {
      this.timelineProgress.speed = 0.0001; // 默认缓慢移动
    }
  }

  // 时间线滚轮控制 - 参考 test.html 的 onMouseScroll
  handleTimelineScroll(event) {
    if (!this.flyStep) return;

    let delta = 0;
    if (event.wheelDelta) {
      delta = event.wheelDelta / 120;
      if (window.opera) delta = -delta;
    } else if (event.detail) {
      delta = -event.detail / 3;
    }

    if (delta > 0) { // 向下滚动
      this.flyStep = this.baseStep;
    } else { // 向上滚动
      this.flyStep = -this.baseStep;
    }

    console.log(`时间线滚轮控制: flyStep = ${this.flyStep}`);
  }

  // 时间线点击动画
  animateTimelineClick(mesh) {
    const originalScale = mesh.userData.scale;

    gsap.to(mesh.scale, {
      x: originalScale * 1.2,
      y: originalScale * 1.2,
      z: originalScale * 1.2,
      duration: 0.2,
      ease: "power2.out",
      onComplete: () => {
        gsap.to(mesh.scale, {
          x: originalScale,
          y: originalScale,
          z: originalScale,
          duration: 0.3,
          ease: "elastic.out(1, 0.5)"
        });
      }
    });
  }

  // 时间线弹窗系统 - 参考 test.html 的 openpop 函数
  openTimelinePopup(timelineData, index) {
    // 创建或更新弹窗内容
    this.showTimelinePopup(timelineData);

    // 相机动画 - 聚焦到选中的时间点
    const targetProgress = (index / (this.timelineData.length - 1)) * 0.8 + 0.1;
    this.timelineProgress.target = targetProgress;

    // 相机移动动画
    const targetPoint = this.timelineCurve.getPointAt(targetProgress);
    gsap.to(this.camera.position, {
      x: targetPoint.x * 0.001,
      y: targetPoint.y * 0.001,
      z: targetPoint.z * 0.001 + 4.5,
      duration: 2,
      ease: "power2.inOut"
    });
  }

  // 显示时间线弹窗
  showTimelinePopup(timelineData) {
    // 移除现有弹窗
    const existingPopup = document.getElementById('timeline-popup');
    if (existingPopup) {
      existingPopup.remove();
    }

    // 创建新弹窗
    const popup = document.createElement('div');
    popup.id = 'timeline-popup';
    popup.innerHTML = `
      <div class="timeline-popup-content">
        <button class="timeline-popup-close">&times;</button>
        <h2>${timelineData.year}</h2>
        <h3>${timelineData.title}</h3>
        <div class="timeline-popup-image">
          <img src="${timelineData.image}" alt="${timelineData.title}" />
        </div>
        <p>这是 ${timelineData.year} 年的美好回忆...</p>
      </div>
    `;

    document.body.appendChild(popup);

    // 弹窗动画
    gsap.fromTo(popup,
      { opacity: 0, scale: 0.8 },
      { opacity: 1, scale: 1, duration: 0.3, ease: "power2.out" }
    );

    // 关闭按钮事件
    popup.querySelector('.timeline-popup-close').addEventListener('click', () => {
      this.closeTimelinePopup();
    });

    // 点击背景关闭
    popup.addEventListener('click', (e) => {
      if (e.target === popup) {
        this.closeTimelinePopup();
      }
    });
  }

  // 关闭时间线弹窗
  closeTimelinePopup() {
    const popup = document.getElementById('timeline-popup');
    if (popup) {
      gsap.to(popup, {
        opacity: 0,
        scale: 0.8,
        duration: 0.2,
        ease: "power2.in",
        onComplete: () => {
          popup.remove();
        }
      });
    }

    // 恢复相机位置
    gsap.to(this.camera.position, {
      x: 0,
      y: 0,
      z: 4.5,
      duration: 1.5,
      ease: "power2.inOut"
    });

    // 恢复时间线移动
    this.timelineProgress.speed = 0.001;
  }

  // 时间线系统更新 - 参考 test.html 的 myrender 函数
  updateTimelineSystem() {
    if (!this.myline || !this.timelineParams) return;

    // 参考 test.html 的时间线进度控制
    if (this.timelineParams.animationView === true) {
      if (this.flyStep > 0 && this.playpercentage >= 1 - 2 / this.ptNum) {
        this.playpercentage = 1 - 2 / this.ptNum;
      } else if (this.flyStep < 0 && this.playpercentage <= 1 / this.ptNum) {
        this.playpercentage = 1 / this.ptNum;
      }
      this.playpercentage += this.flyStep;

      const t = this.playpercentage;

      // 更新时间线点的可见性
      this.updateTimelineVisibility(t);
    }
  }

  // 更新时间线可见性 - 基于当前进度
  updateTimelineVisibility(progress) {
    if (!this.timelineGroup) return;

    // 更新时间线标签的透明度
    this.timelineGroup.children.forEach((label, index) => {
      if (label.userData && label.userData.id) {
        const elementProgress = (index + 1) / this.ptNum;
        const shouldBeVisible = progress >= elementProgress;

        if (label.material) {
          if (shouldBeVisible) {
            // 渐进显示
            const revealProgress = Math.min(1, (progress - elementProgress) * 5);
            label.material.opacity = revealProgress * 0.8;
          } else {
            label.material.opacity = 0;
          }
        }
      }
    });
  }

  // 更新时间线相机 - 参考 test.html 的相机控制
  updateTimelineCamera() {
    if (!this.timelineCurve || !this.timelineProgress) return;

    const t = this.timelineProgress.current;
    const position = new THREE.Vector3();
    const lookAt = new THREE.Vector3();
    const direction = new THREE.Vector3();

    // 获取当前位置和方向
    this.timelineCurve.getPointAt(t, position);
    this.timelineCurve.getTangentAt(t, direction);

    // 计算相机偏移 - 参考 test.html 的偏移计算
    const offset = 15;
    const normal = new THREE.Vector3(0, 1, 0);
    const binormal = new THREE.Vector3().crossVectors(direction, normal);

    // 设置相机位置（在时间线旁边）
    position.add(binormal.clone().multiplyScalar(offset));
    position.add(normal.clone().multiplyScalar(offset * 0.5));

    // 缩放位置到合适的相机距离
    position.multiplyScalar(0.01);
    position.z += 4.5;

    // 平滑相机移动
    if (!this.isRunning) { // 只在非音乐播放时移动相机
      this.camera.position.lerp(position, 0.01);
    }

    // 计算前方注视点
    this.timelineCurve.getPointAt(Math.min(t + 0.1, 1), lookAt);
    lookAt.multiplyScalar(0.01);

    // 让相机看向时间线前方
    if (!this.isRunning) {
      this.camera.lookAt(lookAt);
    }
  }

  // 更新时间线元素 - 渐进显示逻辑
  updateTimelineElements() {
    if (!this.backgroundGroup || !this.timelineProgress) return;

    const currentProgress = this.timelineProgress.current;

    // 更新每个时间线图片的显示状态
    this.backgroundGroup.children.forEach((mesh) => {
      if (!mesh.userData || !mesh.userData.timelineData) return;

      const elementProgress = mesh.userData.timelinePosition; // 0 到 1
      const shouldBeVisible = currentProgress >= elementProgress;

      // 计算显示进度 (当前进度超过元素位置多少)
      const revealProgress = Math.max(0, Math.min(1, (currentProgress - elementProgress) * 5)); // 乘以5加快显示速度

      if (shouldBeVisible && !mesh.userData.isVisible) {
        // 开始显示动画
        mesh.userData.isVisible = true;
        console.log(`显示时间线图片: ${mesh.userData.timelineData.year}`);
      }

      if (mesh.userData.isVisible) {
        // 渐进显示动画
        const targetOpacity = mesh.userData.targetOpacity * revealProgress;
        const targetScale = mesh.userData.targetScale * revealProgress;

        // 平滑过渡
        if (mesh.material) {
          mesh.material.opacity += (targetOpacity - mesh.material.opacity) * 0.1;
        }

        const currentScale = mesh.scale.x;
        const newScale = currentScale + (targetScale - currentScale) * 0.1;
        mesh.scale.setScalar(newScale);

        // 轻微的浮动动画
        const floatOffset = Math.sin(this.time.elapsed * 2 + mesh.userData.index) * 0.1;
        mesh.position.y = mesh.userData.originalPosition.y + floatOffset;

        // 旋转动画
        mesh.rotation.x += mesh.userData.rotationSpeed.x * this.time.delta;
        mesh.rotation.y += mesh.userData.rotationSpeed.y * this.time.delta;
        mesh.rotation.z += mesh.userData.rotationSpeed.z * this.time.delta;

        // 让图片始终面向相机
        mesh.lookAt(this.camera.position);
      } else {
        // 隐藏状态
        if (mesh.material) {
          mesh.material.opacity = 0;
        }
        mesh.scale.setScalar(0.1);
      }
    });

    // 更新标签显示
    if (this.labelGroup) {
      this.labelGroup.children.forEach((label) => {
        if (!label.userData || !label.userData.timelineData) return;

        const elementProgress = label.userData.index / (this.labelGroup.children.length - 1);
        const shouldBeVisible = currentProgress >= elementProgress;
        const revealProgress = Math.max(0, Math.min(1, (currentProgress - elementProgress) * 3));

        if (label.material) {
          label.material.opacity = shouldBeVisible ? revealProgress * 0.8 : 0;
        }
      });
    }
  }
  addHeart() {
    this.heartMaterial = new THREE.ShaderMaterial({
      fragmentShader: document.getElementById("fragmentShader").textContent,
      vertexShader: document.getElementById("vertexShader").textContent,
      uniforms: {
        uTime: { value: 0 },
        uSize: { value: 0.2 },
        uTex: {
          value: new THREE.TextureLoader().load(
            "https://assets.codepen.io/74321/heart.png"
          )
        }
      },
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    const count = this.parameters.count; //2000
    const scales = new Float32Array(count * 1);
    const colors = new Float32Array(count * 3);
    const speeds = new Float32Array(count);
    const randoms = new Float32Array(count);
    const randoms1 = new Float32Array(count);
    const colorChoices = [
      "white",
      "red",
      "pink",
      "crimson",
      "hotpink",
      "green"
    ];

    const squareGeometry = new THREE.PlaneGeometry(1, 1);
    this.instancedGeometry = new THREE.InstancedBufferGeometry();
    Object.keys(squareGeometry.attributes).forEach((attr) => {
      this.instancedGeometry.attributes[attr] = squareGeometry.attributes[attr];
    });
    this.instancedGeometry.index = squareGeometry.index;
    this.instancedGeometry.maxInstancedCount = count;

    for (let i = 0; i < count; i++) {
      const phi = Math.random() * Math.PI * 2;
      const i3 = 3 * i;
      randoms[i] = Math.random();
      randoms1[i] = Math.random();
      scales[i] = Math.random() * 0.35;
      const colorIndex = Math.floor(Math.random() * colorChoices.length);
      const color = new THREE.Color(colorChoices[colorIndex]);
      colors[i3 + 0] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
      speeds[i] = Math.random() * this.parameters.max;
    }
    this.instancedGeometry.setAttribute(
      "random",
      new THREE.InstancedBufferAttribute(randoms, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random1",
      new THREE.InstancedBufferAttribute(randoms1, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aScale",
      new THREE.InstancedBufferAttribute(scales, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aSpeed",
      new THREE.InstancedBufferAttribute(speeds, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aColor",
      new THREE.InstancedBufferAttribute(colors, 3, false)
    );
    this.heart = new THREE.Mesh(this.instancedGeometry, this.heartMaterial);
    console.log(this.heart);
    this.scene.add(this.heart);
  }
  addToScene() {
    this.addBackgroundImages();
    this.addModel();
    this.addHeart();
    this.addSnow();
  }
  async addModel() {
    this.model = await this.loadObj(
      "https://assets.codepen.io/74321/heart.glb"
    );
    this.model.scale.set(0.01, 0.01, 0.01);
    this.model.material = new THREE.MeshMatcapMaterial({
      matcap: this.textureLoader.load(
        "https://assets.codepen.io/74321/3.png",
        () => {
          gsap.to(this.model.scale, {
            x: 0.35,
            y: 0.35,
            z: 0.35,
            duration: 1.5,
            ease: "Elastic.easeOut"
          });
        }
      ),
      color: "#ff89aC"
    });
    this.scene.add(this.model);
  }
  addButton() {
    this.audioBtn = document.querySelector("button");
    this.audioBtn.addEventListener("click", () => {
      this.audioBtn.disabled = true;
      if (this.analyser) {
        this.sound.play();
        this.time.t0 = this.time.elapsed;
        this.data = 0;
        this.isRunning = true;
        gsap.to(this.audioBtn, {
          opacity: 0,
          duration: 1,
          ease: "power1.out"
        });
      } else {
        this.loadMusic().then(() => {
          console.log("music loaded");
        });
      }
    });
  }
  loadObj(path) {
    const loader = new GLTFLoader();
    return new Promise((resolve) => {
      loader.load(
        path,
        (response) => {
          resolve(response.scene.children[0]);
        },
        (xhr) => {},
        (err) => {
          console.log(err);
        }
      );
    });
  }
  loadMusic() {
    return new Promise((resolve) => {
      const listener = new THREE.AudioListener();
      this.camera.add(listener);
      // create a global audio source
      this.sound = new THREE.Audio(listener);
      const audioLoader = new THREE.AudioLoader();
      audioLoader.load(
        // "https://assets.codepen.io/74321/ukulele.mp3",
        "./很爱很爱你.mp3",
        (buffer) => {
          this.sound.setBuffer(buffer);
          this.sound.setLoop(false);
          this.sound.setVolume(0.5);
          this.sound.play();
          this.analyser = new THREE.AudioAnalyser(this.sound, 32);
          // get the average frequency of the sound
          const data = this.analyser.getAverageFrequency();
          this.isRunning = true;
          this.t0 = this.time.elapsed;
          resolve(data);
        },
        (progress) => {
          gsap.to(this.audioBtn, {
            opacity: () => 1 - progress.loaded / progress.total,
            duration: 1,
            ease: "power1.out"
          });
        },

        (error) => {
          console.log(error);
        }
      );
    });
  }
  addSnow() {
    this.snowMaterial = new THREE.ShaderMaterial({
      fragmentShader: document.getElementById("fragmentShader1").textContent,
      vertexShader: document.getElementById("vertexShader1").textContent,
      uniforms: {
        uTime: { value: 0 },
        uSize: { value: 0.3 },
        uTex: {
          value: new THREE.TextureLoader().load(
            "https://assets.codepen.io/74321/heart.png"
          )
        }
      },
      depthWrite: false,
      blending: THREE.AdditiveBlending,
      transparent: true
    });
    const count = 550;
    const scales = new Float32Array(count * 1);
    const colors = new Float32Array(count * 3);
    const phis = new Float32Array(count);
    const randoms = new Float32Array(count);
    const randoms1 = new Float32Array(count);
    const colorChoices = ["red", "pink", "hotpink", "green"];

    const squareGeometry = new THREE.PlaneGeometry(1, 1);
    this.instancedGeometry = new THREE.InstancedBufferGeometry();
    Object.keys(squareGeometry.attributes).forEach((attr) => {
      this.instancedGeometry.attributes[attr] = squareGeometry.attributes[attr];
    });
    this.instancedGeometry.index = squareGeometry.index;
    this.instancedGeometry.maxInstancedCount = count;

    for (let i = 0; i < count; i++) {
      const phi = (Math.random() - 0.5) * 10;
      const i3 = 3 * i;
      phis[i] = phi;
      randoms[i] = Math.random();
      randoms1[i] = Math.random();
      scales[i] = Math.random() * 0.35;
      const colorIndex = Math.floor(Math.random() * colorChoices.length);
      const color = new THREE.Color(colorChoices[colorIndex]);
      colors[i3 + 0] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }
    this.instancedGeometry.setAttribute(
      "phi",
      new THREE.InstancedBufferAttribute(phis, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random",
      new THREE.InstancedBufferAttribute(randoms, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "random1",
      new THREE.InstancedBufferAttribute(randoms1, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aScale",
      new THREE.InstancedBufferAttribute(scales, 1, false)
    );
    this.instancedGeometry.setAttribute(
      "aColor",
      new THREE.InstancedBufferAttribute(colors, 3, false)
    );
    this.snow = new THREE.Mesh(this.instancedGeometry, this.snowMaterial);
    this.scene.add(this.snow);
  }
  
  addBackgroundImages() {
    // 参考 test.html 的时间线系统实现
    this.initTimelineSystem();
  }

  // 初始化时间线系统 - 参考 test.html
  initTimelineSystem() {
    // 时间线参数 - 参考 test.html 的 params
    this.timelineParams = {
      scale: 2,
      extrusionSegments: 100,
      radiusSegments: 5,
      closed: false,
      animationView: true,
      lookAhead: false,
      cameraHelper: false,
      showPath: false,
      moveForward: true,
      moveBackward: false,
    };

    // 年份数组 - 参考 test.html 的 yeararray
    this.yearArray = ["", 2020, 2021, 2022, 2023, 2024, 2025, ""];
    this.ptNum = this.yearArray.length;

    // 时间线数据
    this.timelineData = [
      { year: "2020", image: "assets/images/bg1.svg", title: "初遇" },
      { year: "2021", image: "assets/images/bg2.svg", title: "相识" },
      { year: "2022", image: "assets/images/bg3.svg", title: "相知" },
      { year: "2023", image: "assets/images/bg4.svg", title: "相恋" },
      { year: "2024", image: "assets/images/bg5.svg", title: "相守" },
      { year: "2025", image: "assets/images/bg6.svg", title: "未来" }
    ];

    // 创建时间线曲线 - 参考 test.html 的 myline
    this.myline = new THREE.CatmullRomCurve3([
      new THREE.Vector3(300, 0, 0),
      new THREE.Vector3(300, 0, 300),
      new THREE.Vector3(300, -300, 300),
      new THREE.Vector3(300, -300, 600),
      new THREE.Vector3(300, 0, 600)
    ]);

    // 时间线进度控制 - 参考 test.html 的 playpercentage 和 flyStep
    this.playpercentage = 0;
    this.baseStep = 0.0001;
    this.flyStep = this.baseStep;

    // 创建时间线组
    this.timelineGroup = new THREE.Group();
    this.scene.add(this.timelineGroup);

    // 创建时间线点和标签 - 参考 test.html 的 SceneBuilder
    this.buildTimelinePoints();

    console.log('时间线系统初始化完成');
  }

  // 构建时间线点 - 参考 test.html 的 SceneBuilder.rebuild
  buildTimelinePoints() {
    // 移除现有的时间线点
    const existingPoints = this.scene.getObjectByName("timelinePoints");
    if (existingPoints) {
      this.scene.remove(existingPoints);
    }

    const vertices = [];
    const colors = [];
    const sizes = [];
    let percentage = 0;

    // 颜色配置 - 参考 test.html
    const bkcolor1 = new THREE.Color(0xb700c7);
    const bkcolor2 = new THREE.Color(0xf00000);
    const bkcolor3 = new THREE.Color(0x23d600);
    const bkcolor4 = new THREE.Color(0xffa200);

    for (let i = 0; i < this.ptNum; i++) {
      percentage += 1 / this.ptNum;
      const p1 = this.myline.getPointAt(percentage % 1);

      // 添加顶点位置
      vertices.push(
        p1.x * this.timelineParams.scale,
        p1.y * this.timelineParams.scale,
        p1.z * this.timelineParams.scale
      );
      sizes.push(3);

      // 根据进度设置颜色
      if (percentage > 0.75) {
        colors.push(bkcolor1.r, bkcolor1.g, bkcolor1.b);
      } else if (percentage > 0.5) {
        colors.push(bkcolor2.r, bkcolor2.g, bkcolor2.b);
      } else if (percentage > 0.25) {
        colors.push(bkcolor3.r, bkcolor3.g, bkcolor3.b);
      } else if (percentage > 0) {
        colors.push(bkcolor4.r, bkcolor4.g, bkcolor4.b);
      }

      // 创建年份标签 - 参考 test.html 的 makeLabel
      if (this.yearArray[i].toString().length > 0) {
        this.makeTimelineLabel(
          p1.x * this.timelineParams.scale,
          p1.y * this.timelineParams.scale - 8,
          p1.z * this.timelineParams.scale,
          1200, 600,
          this.yearArray[i].toString(),
          'transparent',
          'white'
        );
      }
    }

    // 创建几何体和材质
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute("position", new THREE.BufferAttribute(new Float32Array(vertices), 3));
    geometry.setAttribute("customColor", new THREE.BufferAttribute(new Float32Array(colors), 3));
    geometry.setAttribute("size", new THREE.BufferAttribute(new Float32Array(sizes), 1));
    geometry.computeBoundingSphere();

    // 使用现有的 shader 材质
    const points = new THREE.Points(geometry, this.heartMaterial);
    points.name = "timelinePoints";
    this.scene.add(points);
  }

  // 创建时间线标签 - 参考 test.html 的 makeLabel 函数
  makeTimelineLabel(x, y, z, labelWidth, size, name, bkcolor, color) {
    const canvas = this.makeLabelCanvas(labelWidth, size, name, bkcolor, color);
    const texture = new THREE.CanvasTexture(canvas);

    texture.minFilter = THREE.LinearFilter;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    const labelMaterial = new THREE.SpriteMaterial({
      map: texture,
      transparent: true,
    });

    const labelBaseScale = 0.01;
    const label = new THREE.Sprite(labelMaterial);
    label.position.set(x, y, z);
    label.scale.set(canvas.width * labelBaseScale, canvas.height * labelBaseScale, 1);
    label.userData = { id: name };
    this.timelineGroup.add(label);
  }

    // 时间线数据 - 参考 test.html 的年份数组概念
    const timelineData = [
      { year: "2020", image: "assets/images/bg1.svg", title: "初遇" },
      { year: "2021", image: "assets/images/bg2.svg", title: "相识" },
      { year: "2022", image: "assets/images/bg3.svg", title: "相知" },
      { year: "2023", image: "assets/images/bg4.svg", title: "相恋" },
      { year: "2024", image: "assets/images/bg5.svg", title: "相守" },
      { year: "2025", image: "assets/images/bg6.svg", title: "未来" }
    ];

    // 创建时间线曲线 - 从远到近的层次感
    const timelineCurve = new THREE.CatmullRomCurve3([
      new THREE.Vector3(-8, 2, -20),   // 最远的起点
      new THREE.Vector3(-4, 1, -15),   // 远景
      new THREE.Vector3(-1, 0, -10),   // 中景
      new THREE.Vector3(1, -0.5, -5),  // 近景
      new THREE.Vector3(3, -1, -2),    // 更近
      new THREE.Vector3(5, -0.5, 2)    // 最近的终点
    ]);

    // 时间线进度控制 - 从最早年份开始
    this.timelineProgress = {
      current: 0.0, // 从最开始开始
      target: 0.0,  // 目标进度
      speed: 0.005, // 较快的移动速度
      min: 0.0,     // 最小进度
      max: 1.0,     // 最大进度
      autoPlay: true, // 自动播放
      direction: 1    // 播放方向 (1: 前进, -1: 后退)
    };

    // 沿着时间线曲线放置图片和标签
    const totalItems = timelineData.length;
    for (let i = 0; i < totalItems; i++) {
      const t = i / (totalItems - 1); // 使用整个曲线
      const point = timelineCurve.getPointAt(t);
      const data = timelineData[i];

      // 创建图片
      this.createTimelineImage(point, data, i, totalItems, timelineConfig);

      // 创建年份标签
      this.createTimelineLabel(point, data, i, timelineConfig);
    }

    // 存储时间线引用
    this.timelineCurve = timelineCurve;
    this.timelineConfig = timelineConfig;
    this.timelineData = timelineData;

    console.log(`时间线系统创建完成: ${totalItems} 个时间点`);
    console.log('时间线进度:', this.timelineProgress);
    console.log('背景图片组:', this.backgroundGroup.children.length);
    console.log('标签组:', this.labelGroup.children.length);
    console.log('相机位置:', this.camera.position);
    console.log('场景中的对象数量:', this.scene.children.length);
  }

  // 创建时间线图片 - 带层次感和渐进显示
  createTimelineImage(point, data, index, total, config) {
    const texture = this.textureLoader.load(
      data.image,
      (loadedTexture) => {
        console.log(`时间线图片加载成功: ${data.year} - ${data.title}`);
        // 图片加载成功后，确保材质更新
        mesh.material.needsUpdate = true;
      },
      undefined,
      (error) => {
        console.warn(`时间线图片加载失败: ${data.image}`, error);
      }
    );

    // 计算基于时间线位置的属性
    const timelinePosition = index / (total - 1); // 0 到 1
    const distanceFromCamera = point.z; // Z轴距离

    // 基于距离计算透明度和缩放
    const distanceFactor = Math.max(0, Math.min(1, (distanceFromCamera + 20) / 25)); // 归一化距离
    const initialOpacity = config.baseOpacity + (config.maxOpacity - config.baseOpacity) * (1 - distanceFactor);
    const initialScale = config.scale + (config.maxScale - config.scale) * (1 - distanceFactor);

    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0, // 初始完全透明
      side: THREE.DoubleSide,
      alphaTest: 0.1
    });

    // 根据距离调整图片大小
    const sizeMultiplier = 0.5 + (1 - distanceFactor) * 1.5; // 远的小，近的大
    const geometry = new THREE.PlaneGeometry(
      config.imageSize * sizeMultiplier,
      config.imageSize * sizeMultiplier
    );
    const mesh = new THREE.Mesh(geometry, material);

    // 设置位置
    mesh.position.copy(point);
    mesh.scale.setScalar(0.1); // 初始很小

    // 让图片面向相机
    mesh.lookAt(this.camera.position);

    // 存储时间线数据
    mesh.userData = {
      timelineData: data,
      index: index,
      total: total,
      timelinePosition: timelinePosition,
      originalPosition: point.clone(),
      targetOpacity: initialOpacity,
      targetScale: initialScale,
      distanceFactor: distanceFactor,
      isVisible: false, // 初始不可见
      rotationSpeed: {
        x: (Math.random() - 0.5) * config.rotationSpeed,
        y: (Math.random() - 0.5) * config.rotationSpeed,
        z: (Math.random() - 0.5) * config.rotationSpeed * 0.5
      }
    };

    this.backgroundGroup.add(mesh);

    // 调试信息
    console.log(`创建时间线图片 ${index}: ${data.year} 位置 (${point.x.toFixed(2)}, ${point.y.toFixed(2)}, ${point.z.toFixed(2)}) 距离因子: ${distanceFactor.toFixed(2)}`);
  }

  // 创建时间线标签 - 参考 test.html 的 makeLabel 函数
  createTimelineLabel(point, data, index, config) {
    const canvas = this.makeLabelCanvas(120, 24, data.year, 'rgba(0,0,0,0.7)', 'white');
    const texture = new THREE.CanvasTexture(canvas);
    texture.minFilter = THREE.LinearFilter;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    const labelMaterial = new THREE.SpriteMaterial({
      map: texture,
      transparent: true,
    });

    const labelBaseScale = 0.05; // 增大标签缩放
    const label = new THREE.Sprite(labelMaterial);
    label.position.copy(point);
    label.position.y -= 1; // 减小偏移，让标签更靠近图片
    label.position.z += config.depth + 1; // 稍微前移
    label.scale.set(
      canvas.width * labelBaseScale,
      canvas.height * labelBaseScale,
      1
    );

    // 存储标签数据
    label.userData = {
      timelineData: data,
      index: index,
      type: 'label'
    };

    this.labelGroup.add(label);

    // 调试信息
    console.log(`创建时间线标签 ${index}: ${data.year} 位置 (${label.position.x.toFixed(2)}, ${label.position.y.toFixed(2)}, ${label.position.z.toFixed(2)})`);
  }

  // 创建测试图片，确保图片系统工作
  createTestImage(config) {
    // 创建一个简单的测试纹理
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    // 绘制一个彩色方块
    ctx.fillStyle = '#ff89aC';
    ctx.fillRect(0, 0, 64, 64);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('TEST', 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.9
    });

    const geometry = new THREE.PlaneGeometry(2, 2);
    const mesh = new THREE.Mesh(geometry, material);

    // 放置在相机前面，确保可见
    mesh.position.set(0, 0, -3);
    mesh.lookAt(this.camera.position);

    this.backgroundGroup.add(mesh);
    console.log('测试图片已创建，位置:', mesh.position);
  }

  // 创建简单的线性时间线布局
  createSimpleTimelineLayout(timelineData, config) {
    console.log('创建简单时间线布局...');

    timelineData.forEach((data, index) => {
      // 简单的线性排列
      const x = (index - timelineData.length / 2) * 2; // 水平间距2单位
      const y = Math.sin(index * 0.5) * 0.5; // 轻微的波浪形
      const z = -3; // 固定深度

      // 创建图片
      const texture = this.textureLoader.load(
        data.image,
        (loadedTexture) => {
          console.log(`简单布局图片加载成功: ${data.year}`);
        },
        undefined,
        (error) => {
          console.warn(`简单布局图片加载失败: ${data.image}`, error);
          // 创建备用颜色方块
          this.createColorBlock(x, y, z, data, index);
        }
      );

      const material = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        opacity: config.opacity,
        side: THREE.DoubleSide
      });

      const geometry = new THREE.PlaneGeometry(1.5, 1.5); // 固定大小，确保可见
      const mesh = new THREE.Mesh(geometry, material);

      mesh.position.set(x, y, z);
      mesh.lookAt(this.camera.position);

      mesh.userData = {
        timelineData: data,
        index: index,
        scale: config.scale
      };

      this.backgroundGroup.add(mesh);
      console.log(`简单布局图片 ${index}: ${data.year} 位置 (${x}, ${y}, ${z})`);
    });
  }

  // 创建颜色方块作为备用
  createColorBlock(x, y, z, data, index) {
    const colors = ['#ff89aC', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff6b6b'];
    const color = colors[index % colors.length];

    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    ctx.fillStyle = color;
    ctx.fillRect(0, 0, 64, 64);
    ctx.fillStyle = 'white';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(data.year, 32, 36);

    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.8
    });

    const geometry = new THREE.PlaneGeometry(1, 1);
    const mesh = new THREE.Mesh(geometry, material);

    mesh.position.set(x, y, z);
    mesh.lookAt(this.camera.position);

    mesh.userData = {
      timelineData: data,
      index: index,
      scale: 1
    };

    this.backgroundGroup.add(mesh);
    console.log(`颜色方块 ${index}: ${data.year} 位置 (${x}, ${y}, ${z})`);
  }

  // 创建标签画布 - 参考 test.html 的 makeLabelCanvas 函数
  makeLabelCanvas(baseWidth, size, name, bkcolor, color) {
    const borderSize = 2;
    const ctx = document.createElement('canvas').getContext('2d');
    const font = `bold ${size}px Arial, sans-serif`;
    ctx.font = font;

    const textWidth = ctx.measureText(name).width;
    const doubleBorderSize = borderSize * 2;
    const width = Math.max(baseWidth, textWidth + doubleBorderSize);
    const height = size + doubleBorderSize;

    ctx.canvas.width = width;
    ctx.canvas.height = height;

    // 重新设置字体
    ctx.font = font;
    ctx.textBaseline = 'middle';
    ctx.textAlign = 'center';

    // 背景
    ctx.fillStyle = bkcolor;
    ctx.fillRect(0, 0, width, height);

    // 文字
    const scaleFactor = Math.min(1, baseWidth / textWidth);
    ctx.translate(width / 2, height / 2);
    ctx.scale(scaleFactor, 1);
    ctx.fillStyle = color;
    ctx.fillText(name, 0, 0);

    return ctx.canvas;
  }

  // 初始化时间线提示
  initTimelineHint() {
    const hint = document.getElementById('timeline-hint');
    if (hint) {
      // 5秒后自动隐藏提示
      setTimeout(() => {
        gsap.to(hint, {
          opacity: 0,
          y: 20,
          duration: 0.5,
          ease: "power2.in",
          onComplete: () => {
            hint.style.display = 'none';
          }
        });
      }, 5000);

      // 点击提示隐藏
      hint.addEventListener('click', () => {
        gsap.to(hint, {
          opacity: 0,
          y: 20,
          duration: 0.3,
          ease: "power2.in",
          onComplete: () => {
            hint.style.display = 'none';
          }
        });
      });
    }
  }
}

const world = new World({
  canvas: document.querySelector("canvas.webgl"),
  cameraPosition: { x: 0, y: 0, z: 4.5 }
});

world.loop();
