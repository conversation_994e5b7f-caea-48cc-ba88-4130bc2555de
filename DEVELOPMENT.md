# 开发指南

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```
访问 `http://localhost:3000`

### 3. 构建生产版本
```bash
npm run build
```

### 4. 预览生产版本
```bash
npm run preview
```
访问 `http://localhost:4173`

## 📝 修改指南

### 修改标题
编辑 `src/index.html` 中的 `<h1>` 标签：
```html
<h1>
  你的标题
</h1>
```

### 修改颜色主题
编辑 `src/script.js` 中的 `colorChoices` 数组：
```javascript
const colorChoices = [
  "white",
  "red", 
  "pink",
  "crimson",
  "hotpink",
  "green"
];
```

### 修改动画参数
编辑 `src/script.js` 中的 `parameters` 对象：
```javascript
this.parameters = {
  count: 1500,        // 粒子数量
  max: 12.5 * Math.PI, // 最大动画范围
  a: 2,               // 相机移动参数
  c: 4.5              // 相机距离参数
};
```

### 修改音乐
1. 将新的音频文件放入 `dist/` 目录
2. 修改 `src/script.js` 中的音频路径：
```javascript
audioLoader.load(
  "你的音频文件路径.mp3",
  // ...
);
```

### 修改背景颜色
编辑 `src/style.css` 中的背景色：
```css
body {
  background: #16000a; /* 修改这里的颜色 */
}
```

## 🎨 自定义动画效果

### 修改粒子效果
在 `src/script.js` 中找到 `addHeart()` 和 `addSnow()` 方法，修改：
- 粒子数量：`count` 变量
- 粒子大小：`scales[i]` 计算
- 粒子颜色：`colorChoices` 数组
- 动画速度：`speeds[i]` 计算

### 修改着色器
编辑 `src/index.html` 中的着色器脚本：
- `vertexShader` - 顶点着色器
- `fragmentShader` - 片段着色器
- `vertexShader1` - 第二个顶点着色器
- `fragmentShader1` - 第二个片段着色器

## 🔧 开发技巧

### 热重载
开发模式下，修改文件后会自动刷新浏览器

### 调试
1. 打开浏览器开发者工具
2. 查看 Console 面板的错误信息
3. 使用 Network 面板检查资源加载

### 性能优化
1. 减少粒子数量以提高性能
2. 调整着色器复杂度
3. 优化纹理大小

## 📁 文件结构说明

```
src/
├── index.html    # 主页面，包含着色器脚本
├── script.js     # 主要逻辑，Three.js 代码
└── style.css     # 样式文件

dist/             # 构建输出目录
├── index.html    # 构建后的主页面
├── assets/       # 构建后的资源文件
└── live.ogg      # 音频文件
```

## 🐛 常见问题

### 1. 构建失败
- 检查 Node.js 版本（需要 >= 16.0.0）
- 删除 `node_modules` 重新安装依赖

### 2. 音频无法播放
- 确保音频文件路径正确
- 检查浏览器是否支持音频格式
- 某些浏览器需要用户交互才能播放音频

### 3. 3D 效果不显示
- 检查 WebGL 支持
- 查看浏览器控制台错误信息
- 确保 Three.js 库正确加载

### 4. 性能问题
- 减少粒子数量
- 简化着色器代码
- 使用更小的纹理

## 📚 学习资源

- [Three.js 官方文档](https://threejs.org/docs/)
- [WebGL 着色器教程](https://webglfundamentals.org/)
- [GSAP 动画库](https://greensock.com/gsap/)
- [Vite 构建工具](https://vitejs.dev/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE.txt](LICENSE.txt) 