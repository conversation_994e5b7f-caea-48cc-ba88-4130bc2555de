# 时间线背景图片功能

## 🎯 功能概述

新的时间线背景图片功能将图片沿着S形曲线路径分布，创建类似时间线的视觉效果。每张图片都会缓慢移动并旋转，形成动态的时间线效果。

## ✨ 主要特性

### 1. 曲线路径分布
- 图片沿着S形曲线路径分布
- 每张图片占据路径上的一个固定位置
- 路径从左上角延伸到右下角

### 2. 动态动画
- 图片会缓慢沿着曲线移动
- 每张图片独立旋转
- 音乐播放时旋转速度加快

### 3. 交互功能
- 点击图片会有缩放动画效果
- 图片始终面向相机
- 支持鼠标悬停检测

### 4. 音乐响应
- 播放音乐时图片旋转速度增加
- 音乐频率越高，动画越活跃

## ⚙️ 配置参数

在 `src/script.js` 中可以调整以下参数：

```javascript
const timelineConfig = {
  imageSize: 60,        // 图片大小（像素）
  spacing: 0.05,        // 路径上的间距
  depth: -15,           // 背景深度（Z轴位置）
  rotationSpeed: 0.0002, // 旋转速度
  opacity: 0.6,         // 透明度
  curveSegments: 100    // 曲线分段数
};
```

## 🎨 自定义选项

### 调整图片大小
```javascript
imageSize: 80  // 更大的图片
imageSize: 40  // 更小的图片
```

### 调整透明度
```javascript
opacity: 0.8   // 更明显的图片
opacity: 0.3   // 更淡的图片
```

### 调整旋转速度
```javascript
rotationSpeed: 0.001   // 更快的旋转
rotationSpeed: 0.0001  // 更慢的旋转
```

### 调整背景深度
```javascript
depth: -10  // 更靠近相机
depth: -30  // 更远离相机
```

## 🔧 路径自定义

可以修改曲线路径来改变图片分布：

```javascript
const curve = new THREE.CatmullRomCurve3([
  new THREE.Vector3(-300, 0, -200),   // 起点
  new THREE.Vector3(-200, 100, -100), // 控制点1
  new THREE.Vector3(0, 0, 0),         // 中心点
  new THREE.Vector3(200, -100, 100),  // 控制点2
  new THREE.Vector3(300, 0, 200)      // 终点
]);
```

## 🎵 音乐响应配置

音乐响应强度可以通过以下参数调整：

```javascript
// 在动画循环中
speedMultiplier = 1 + this.data * 0.5;  // 音乐响应强度
```

## 📁 图片管理

### 添加新图片
1. 将图片文件放入 `public/images/` 目录
2. 在 `imageUrls` 数组中添加路径：

```javascript
const imageUrls = [
  "/images/bg1.svg",
  "/images/bg2.svg",
  "/images/bg3.svg",
  "/images/bg4.svg",
  "/images/bg5.svg",
  "/images/bg6.svg",
  "/images/your-new-image.svg"  // 添加新图片
];
```

### 支持的图片格式
- SVG（推荐，文件小，可缩放）
- PNG
- JPG
- WebP

## 🎮 交互功能

### 点击效果
- 点击图片会有1.2倍缩放动画
- 动画持续0.4秒
- 控制台会显示点击的图片索引

### 鼠标悬停
- 图片会自动面向相机
- 支持鼠标移动检测

## 🐛 常见问题

### 1. 图片不显示
- 检查图片路径是否正确
- 确保图片文件存在于 `public/images/` 目录
- 查看浏览器控制台的错误信息

### 2. 动画卡顿
- 减少图片数量
- 降低旋转速度
- 使用更小的图片文件

### 3. 图片重叠
- 调整 `spacing` 参数
- 修改曲线路径
- 减少图片数量

## 🎯 性能优化建议

1. **图片优化**
   - 使用SVG格式（文件小，可缩放）
   - 压缩图片文件大小
   - 使用适当的图片尺寸

2. **动画优化**
   - 控制图片数量（建议6-10张）
   - 调整旋转速度
   - 使用适当的透明度

3. **交互优化**
   - 限制同时动画的图片数量
   - 使用节流函数处理鼠标事件

## 📚 扩展功能

### 添加图片标签
```javascript
// 为每张图片添加标签
const label = new THREE.Sprite(labelMaterial);
label.position.copy(point);
label.position.y += 40;
this.backgroundGroup.add(label);
```

### 添加连接线
```javascript
// 在图片之间添加连接线
const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff });
const line = new THREE.Line(lineGeometry, lineMaterial);
this.backgroundGroup.add(line);
```

### 添加粒子效果
```javascript
// 在图片周围添加粒子
const particles = new THREE.Points(particleGeometry, particleMaterial);
this.backgroundGroup.add(particles);
```

## 🎨 视觉效果建议

1. **颜色搭配**：选择与主题相关的图片颜色
2. **动画节奏**：调整移动和旋转速度
3. **层次感**：通过深度和透明度创建层次
4. **音乐同步**：让动画与音乐节奏同步

## 📝 更新日志

- **v1.0**：基础时间线功能
- **v1.1**：添加音乐响应
- **v1.2**：添加鼠标交互
- **v1.3**：优化动画性能 