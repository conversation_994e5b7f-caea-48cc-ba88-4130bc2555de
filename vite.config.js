import { defineConfig } from 'vite'

export default defineConfig({
  root: 'src',
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    assetsDir: 'assets',
    rollupOptions: {
      input: {
        main: 'src/index.html'
      }
    },
    target: 'es2015'
  },
  server: {
    port: 3000,
    open: true
  },
  preview: {
    port: 4173,
    open: true
  },
  optimizeDeps: {
    include: ['three', 'gsap']
  },
  publicDir: 'assets',
  assetsInclude: ['**/*.jpg', '**/*.png', '**/*.gif', '**/*.webp']
}) 